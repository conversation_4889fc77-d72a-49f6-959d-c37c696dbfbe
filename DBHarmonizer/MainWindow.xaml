<Window x:Class="DBHarmonizer.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:DBHarmonizer"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="Oracle Database Schema Structure Compare" 
        Height="900" Width="1400"
        MinHeight="700" MinWidth="1200"
        Background="#F8FAFC"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized">
    
    <Window.Resources>
        <!-- Status to Color Converter -->
        <local:ComparisonStatusToColorConverter x:Key="StatusToColorConverter"/>
        <local:ComparisonStatusToIconConverter x:Key="StatusToIconConverter"/>
        
        <!-- Navy Blue Color Palette -->
        <SolidColorBrush x:Key="PrimaryNavyBrush" Color="#1E3A8A"/>
        <SolidColorBrush x:Key="SecondaryNavyBrush" Color="#3B82F6"/>
        <SolidColorBrush x:Key="LightNavyBrush" Color="#60A5FA"/>
        <SolidColorBrush x:Key="DarkNavyBrush" Color="#1E40AF"/>
        <SolidColorBrush x:Key="AccentNavyBrush" Color="#2563EB"/>
        
        <!-- Background Colors -->
        <SolidColorBrush x:Key="MainBackgroundBrush" Color="#F8FAFC"/>
        <SolidColorBrush x:Key="SecondaryBackgroundBrush" Color="#F1F5F9"/>
        <SolidColorBrush x:Key="CardBackgroundBrush" Color="#FFFFFF"/>
        
        <!-- Text Colors -->
        <SolidColorBrush x:Key="PrimaryTextBrush" Color="#0F172A"/>
        <SolidColorBrush x:Key="SecondaryTextBrush" Color="#475569"/>
        <SolidColorBrush x:Key="AccentTextBrush" Color="#1E3A8A"/>
        
        <!-- Border Colors -->
        <SolidColorBrush x:Key="BorderBrush" Color="#E2E8F0"/>
        <SolidColorBrush x:Key="FocusBorderBrush" Color="#3B82F6"/>
        
        <!-- Status Colors -->
        <SolidColorBrush x:Key="SuccessBrush" Color="#10B981"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#F59E0B"/>
        <SolidColorBrush x:Key="ErrorBrush" Color="#EF4444"/>
        <SolidColorBrush x:Key="InfoBrush" Color="#3B82F6"/>
        
        <!-- Progress and Loading Colors -->
        <SolidColorBrush x:Key="ProgressBrush" Color="#3B82F6"/>
        <SolidColorBrush x:Key="ProgressBackgroundBrush" Color="#E5E7EB"/>
        
        <!-- Help and Tooltip Colors -->
        <SolidColorBrush x:Key="HelpTextBrush" Color="#6B7280"/>
        <SolidColorBrush x:Key="TooltipBackgroundBrush" Color="#374151"/>
        <SolidColorBrush x:Key="TooltipTextBrush" Color="#F9FAFB"/>
        
        <!-- Card Style -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource CardBackgroundBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#E2E8F0" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Header Style -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryNavyBrush}"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
        </Style>
        
        <!-- Button Style -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryNavyBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="{StaticResource PrimaryNavyBrush}"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="1" 
                                CornerRadius="6" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Material Design ComboBox Style -->
        <Style x:Key="MaterialComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBox">
                        <Grid>
                            <Border x:Name="Border"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="6">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <!-- Content Presenter -->
                                    <ContentPresenter x:Name="ContentSite"
                                                      Grid.Column="0"
                                                      IsHitTestVisible="False"
                                                      Content="{TemplateBinding SelectionBoxItem}"
                                                      ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                      ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                                      Margin="{TemplateBinding Padding}"
                                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                      HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"/>
                                    
                                    <!-- Dropdown Arrow -->
                                    <Border x:Name="DropDownArrow"
                                            Grid.Column="1"
                                            Width="30"
                                            Height="{TemplateBinding Height}"
                                            Background="Transparent">
                                        <Path x:Name="ArrowPath"
                                              Data="M 0 0 L 4 4 L 8 0 Z"
                                              Fill="{StaticResource SecondaryTextBrush}"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                                    </Border>
                                    
                                    <!-- Toggle Button (Invisible) -->
                                    <ToggleButton x:Name="ToggleButton"
                                                  Grid.ColumnSpan="2"
                                                  Focusable="False"
                                                  IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                                  ClickMode="Press"
                                                  Background="Transparent"
                                                  BorderThickness="0"/>
                                </Grid>
                            </Border>
                            
                            <!-- Popup -->
                            <Popup x:Name="Popup"
                                   Placement="Bottom"
                                   IsOpen="{TemplateBinding IsDropDownOpen}"
                                   AllowsTransparency="True"
                                   Focusable="False"
                                   PopupAnimation="Slide">
                                <Grid x:Name="DropDown"
                                      SnapsToDevicePixels="True"
                                      MinWidth="{TemplateBinding ActualWidth}"
                                      MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                    <Border x:Name="DropDownBorder"
                                            Background="White"
                                            BorderBrush="{StaticResource BorderBrush}"
                                            BorderThickness="1"
                                            CornerRadius="6"
                                            Margin="0,2,0,0">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#E2E8F0" Direction="270" ShadowDepth="4" BlurRadius="12" Opacity="0.4"/>
                                        </Border.Effect>
                                        <ScrollViewer x:Name="DropDownScrollViewer"
                                                      Margin="1"
                                                      SnapsToDevicePixels="True">
                                            <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained"/>
                                        </ScrollViewer>
                                    </Border>
                                </Grid>
                            </Popup>
                        </Grid>
                        
                        <ControlTemplate.Triggers>
                            <!-- Hover State -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource SecondaryNavyBrush}"/>
                                <Setter TargetName="ArrowPath" Property="Fill" Value="{StaticResource SecondaryNavyBrush}"/>
                            </Trigger>
                            
                            <!-- Focus State -->
                            <Trigger Property="IsFocused" Value="True">
                                <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource PrimaryNavyBrush}"/>
                                <Setter TargetName="Border" Property="BorderThickness" Value="2"/>
                                <Setter TargetName="ArrowPath" Property="Fill" Value="{StaticResource PrimaryNavyBrush}"/>
                            </Trigger>
                            
                            <!-- Dropdown Open State -->
                            <Trigger Property="IsDropDownOpen" Value="True">
                                <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource PrimaryNavyBrush}"/>
                                <Setter TargetName="Border" Property="BorderThickness" Value="2"/>
                                <Setter TargetName="ArrowPath" Property="Data" Value="M 0 4 L 4 0 L 8 4 Z"/>
                                <Setter TargetName="ArrowPath" Property="Fill" Value="{StaticResource PrimaryNavyBrush}"/>
                            </Trigger>
                            
                            <!-- Disabled State -->
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="Border" Property="Background" Value="#F8F9FA"/>
                                <Setter TargetName="Border" Property="BorderBrush" Value="#DEE2E6"/>
                                <Setter TargetName="ContentSite" Property="Opacity" Value="0.6"/>
                                <Setter TargetName="ArrowPath" Property="Fill" Value="#ADB5BD"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Material Design ComboBoxItem Style -->
        <Style x:Key="MaterialComboBoxItemStyle" TargetType="ComboBoxItem">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBoxItem">
                        <Border x:Name="Border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Left" VerticalAlignment="Center"/>
                        </Border>
                        
                        <ControlTemplate.Triggers>
                            <!-- Hover State -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#E3F2FD"/>
                            </Trigger>
                            
                            <!-- Selected State -->
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="{StaticResource SecondaryNavyBrush}"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                            
                            <!-- Highlighted State -->
                            <Trigger Property="IsHighlighted" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#BBDEFB"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Material Design TextBox Style -->
        <Style x:Key="MaterialTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border x:Name="Border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ScrollViewer x:Name="PART_ContentHost"
                                          Margin="{TemplateBinding Padding}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                        </Border>
                        
                        <ControlTemplate.Triggers>
                            <!-- Hover State -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource SecondaryNavyBrush}"/>
                            </Trigger>
                            
                            <!-- Focus State -->
                            <Trigger Property="IsFocused" Value="True">
                                <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource PrimaryNavyBrush}"/>
                                <Setter TargetName="Border" Property="BorderThickness" Value="2"/>
                            </Trigger>
                            
                            <!-- Disabled State -->
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="Border" Property="Background" Value="#F8F9FA"/>
                                <Setter TargetName="Border" Property="BorderBrush" Value="#DEE2E6"/>
                                <Setter Property="Foreground" Value="#ADB5BD"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Material Design PasswordBox Style -->
        <Style x:Key="MaterialPasswordBoxStyle" TargetType="PasswordBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="PasswordBox">
                        <Border x:Name="Border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ScrollViewer x:Name="PART_ContentHost"
                                          Margin="{TemplateBinding Padding}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                        </Border>
                        
                        <ControlTemplate.Triggers>
                            <!-- Hover State -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource SecondaryNavyBrush}"/>
                            </Trigger>
                            
                            <!-- Focus State -->
                            <Trigger Property="IsFocused" Value="True">
                                <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource PrimaryNavyBrush}"/>
                                <Setter TargetName="Border" Property="BorderThickness" Value="2"/>
                            </Trigger>
                            
                            <!-- Disabled State -->
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="Border" Property="Background" Value="#F8F9FA"/>
                                <Setter TargetName="Border" Property="BorderBrush" Value="#DEE2E6"/>
                                <Setter Property="Foreground" Value="#ADB5BD"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Material Design TabControl Style -->
        <Style x:Key="MaterialTabControlStyle" TargetType="TabControl">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TabControl">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            
                            <!-- Tab Headers Container -->
                            <Border Grid.Row="0" 
                                    Background="White"
                                    BorderBrush="{StaticResource BorderBrush}"
                                    BorderThickness="0,0,0,1">
                                <TabPanel x:Name="HeaderPanel"
                                          IsItemsHost="True"
                                          Background="Transparent"
                                          Margin="0"/>
                            </Border>
                            
                            <!-- Tab Content Container -->
                            <Border Grid.Row="1"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    Padding="{TemplateBinding Padding}">
                                <ContentPresenter x:Name="PART_SelectedContentHost"
                                                  ContentSource="SelectedContent"/>
                            </Border>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Material Design TabItem Style -->
        <Style x:Key="MaterialTabItemStyle" TargetType="TabItem">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="{StaticResource SecondaryTextBrush}"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Padding" Value="24,12"/>
            <Setter Property="Margin" Value="0"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TabItem">
                        <Grid>
                            <Border x:Name="Border"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    Padding="{TemplateBinding Padding}">
                                <ContentPresenter x:Name="ContentSite"
                                                  ContentSource="Header"
                                                  HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                  VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                  RecognizesAccessKey="True"/>
                            </Border>
                            
                            <!-- Active Tab Indicator -->
                            <Rectangle x:Name="ActiveIndicator"
                                       Height="3"
                                       VerticalAlignment="Bottom"
                                       Fill="{StaticResource PrimaryNavyBrush}"
                                       Opacity="0"/>
                        </Grid>
                        
                        <ControlTemplate.Triggers>
                            <!-- Hover State -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#F8F9FA"/>
                                <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
                            </Trigger>
                            
                            <!-- Selected State -->
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="White"/>
                                <Setter Property="Foreground" Value="{StaticResource PrimaryNavyBrush}"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                                <Setter TargetName="ActiveIndicator" Property="Opacity" Value="1"/>
                            </Trigger>
                            
                            <!-- Selected + Hover State -->
                            <MultiTrigger>
                                <MultiTrigger.Conditions>
                                    <Condition Property="IsSelected" Value="True"/>
                                    <Condition Property="IsMouseOver" Value="True"/>
                                </MultiTrigger.Conditions>
                                <Setter TargetName="Border" Property="Background" Value="White"/>
                                <Setter Property="Foreground" Value="{StaticResource PrimaryNavyBrush}"/>
                            </MultiTrigger>
                            
                            <!-- Disabled State -->
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Foreground" Value="#ADB5BD"/>
                                <Setter TargetName="Border" Property="Background" Value="Transparent"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Material Design Code Editor TextBox Style -->
        <Style x:Key="MaterialCodeEditorStyle" TargetType="TextBox">
            <Setter Property="Background" Value="#FAFAFA"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Padding" Value="12"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border x:Name="Border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ScrollViewer x:Name="PART_ContentHost"
                                          Margin="{TemplateBinding Padding}"
                                          VerticalScrollBarVisibility="{TemplateBinding VerticalScrollBarVisibility}"
                                          HorizontalScrollBarVisibility="{TemplateBinding HorizontalScrollBarVisibility}"/>
                        </Border>
                        
                        <ControlTemplate.Triggers>
                            <!-- Hover State -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource SecondaryNavyBrush}"/>
                            </Trigger>
                            
                            <!-- Focus State -->
                            <Trigger Property="IsFocused" Value="True">
                                <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource PrimaryNavyBrush}"/>
                                <Setter TargetName="Border" Property="BorderThickness" Value="2"/>
                                <Setter TargetName="Border" Property="Background" Value="White"/>
                            </Trigger>
                            
                            <!-- ReadOnly State -->
                            <Trigger Property="IsReadOnly" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#F8F9FA"/>
                                <Setter TargetName="Border" Property="BorderBrush" Value="#DEE2E6"/>
                            </Trigger>
                            
                            <!-- Disabled State -->
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="Border" Property="Background" Value="#F8F9FA"/>
                                <Setter TargetName="Border" Property="BorderBrush" Value="#DEE2E6"/>
                                <Setter Property="Foreground" Value="#ADB5BD"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Enhanced Tooltip Style -->
        <Style x:Key="EnhancedTooltipStyle" TargetType="ToolTip">
            <Setter Property="Background" Value="{StaticResource TooltipBackgroundBrush}"/>
            <Setter Property="Foreground" Value="{StaticResource TooltipTextBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="MaxWidth" Value="300"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ToolTip">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" Opacity="0.2" BlurRadius="8" ShadowDepth="2"/>
                            </Border.Effect>
                            <ContentPresenter>
                                <ContentPresenter.Resources>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="TextWrapping" Value="Wrap"/>
                                    </Style>
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Help Text Style -->
        <Style x:Key="HelpTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="{StaticResource HelpTextBrush}"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="LineHeight" Value="16"/>
            <Setter Property="Margin" Value="0,4,0,0"/>
        </Style>
        
        <!-- Step Indicator Style -->
        <Style x:Key="StepIndicatorStyle" TargetType="Border">
            <Setter Property="Width" Value="32"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="CornerRadius" Value="16"/>
            <Setter Property="Background" Value="{StaticResource ProgressBackgroundBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
        
        <!-- Active Step Indicator Style -->
        <Style x:Key="ActiveStepIndicatorStyle" TargetType="Border" BasedOn="{StaticResource StepIndicatorStyle}">
            <Setter Property="Background" Value="{StaticResource ProgressBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource ProgressBrush}"/>
        </Style>
        
        <!-- Completed Step Indicator Style -->
        <Style x:Key="CompletedStepIndicatorStyle" TargetType="Border" BasedOn="{StaticResource StepIndicatorStyle}">
            <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource SuccessBrush}"/>
        </Style>
        
        <!-- Progress Bar Style -->
        <Style x:Key="MaterialProgressBarStyle" TargetType="ProgressBar">
            <Setter Property="Background" Value="{StaticResource ProgressBackgroundBrush}"/>
            <Setter Property="Foreground" Value="{StaticResource ProgressBrush}"/>
            <Setter Property="Height" Value="6"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ProgressBar">
                        <Border Background="{TemplateBinding Background}" CornerRadius="3">
                            <Grid>
                                <Rectangle x:Name="PART_Track" Fill="{TemplateBinding Background}" RadiusX="3" RadiusY="3"/>
                                <Rectangle x:Name="PART_Indicator" 
                                           Fill="{TemplateBinding Foreground}" 
                                           RadiusX="3" RadiusY="3"
                                           HorizontalAlignment="Left"/>
                            </Grid>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Loading Spinner Style -->
        <Style x:Key="LoadingSpinnerStyle" TargetType="Border">
            <Setter Property="Width" Value="24"/>
            <Setter Property="Height" Value="24"/>
            <Setter Property="BorderBrush" Value="{StaticResource ProgressBrush}"/>
            <Setter Property="BorderThickness" Value="3"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Opacity" Value="0.6"/>
            <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
            <Setter Property="RenderTransform">
                <Setter.Value>
                    <RotateTransform Angle="0"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsVisible" Value="True">
                    <Trigger.EnterActions>
                        <BeginStoryboard>
                            <Storyboard RepeatBehavior="Forever">
                                <DoubleAnimation Storyboard.TargetProperty="RenderTransform.Angle"
                                                 From="0" To="360" Duration="0:0:1"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </Trigger.EnterActions>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <!-- Section Header Style -->
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>
        
        <!-- Instruction Text Style -->
        <Style x:Key="InstructionTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="{StaticResource SecondaryTextBrush}"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="LineHeight" Value="20"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryNavyBrush}" Padding="20,16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🗄️" FontSize="24" Foreground="White" Margin="0,0,12,0"/>
                    <TextBlock Text="Oracle Database Schema Structure Compare" 
                               FontSize="20" FontWeight="SemiBold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="SettingsButton" Content="Settings" Style="{StaticResource PrimaryButtonStyle}" 
                            Background="{StaticResource SecondaryNavyBrush}" Margin="0,0,8,0" Click="SettingsButton_Click"/>
                    <Button x:Name="AboutButton" Content="About" Style="{StaticResource PrimaryButtonStyle}" 
                            Background="{StaticResource SecondaryNavyBrush}" Click="AboutButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main TabControl -->
        <TabControl Grid.Row="1" x:Name="MainTabControl" 
                    Style="{StaticResource MaterialTabControlStyle}" 
                    Margin="16,16,16,8"
                    SelectionChanged="MainTabControl_SelectionChanged">
            
            <!-- Step 1: Database Connections -->
            <TabItem x:Name="ConnectionsTab" Style="{StaticResource MaterialTabItemStyle}">
                <TabItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <Border Style="{StaticResource ActiveStepIndicatorStyle}" Margin="0,0,8,0" Width="24" Height="24">
                            <TextBlock Text="1" FontWeight="Bold" FontSize="12" 
                                       Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock Text="Database Connections" VerticalAlignment="Center"/>
                    </StackPanel>
                </TabItem.Header>
                
                <Border Style="{StaticResource CardStyle}" Margin="0">
                   <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <!-- Step 1 Header -->
                        <StackPanel Grid.Row="0" Margin="0,0,0,16">
                             <TextBlock Text="Configure Database Connections" Style="{StaticResource SectionHeaderStyle}"/>
                            <TextBlock Text="Enter connection details for both source and target Oracle databases. Test each connection to ensure they're working properly before proceeding." 
                                       Style="{StaticResource InstructionTextStyle}"/>
                        </StackPanel>
                        
                        <!-- Progress Bar -->
                        <ProgressBar Grid.Row="1" x:Name="ConnectionProgressBar" 
                                     Style="{StaticResource MaterialProgressBarStyle}" 
                                     Margin="0,0,0,16" Visibility="Collapsed"/>
                        
                        <!-- Connection Forms -->
                <Grid Grid.Row="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Source Database -->
                    <StackPanel Grid.Column="0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Source Database" Style="{StaticResource HeaderTextStyle}"/>
                            <Border Grid.Column="1" x:Name="SourceStatusIndicator" Width="12" Height="12" 
                                    CornerRadius="6" Background="{StaticResource ProgressBackgroundBrush}" 
                                    ToolTip="Connection Status" Margin="8,0,0,0"/>
                        </Grid>
                        
                        <TextBlock Text="This is the database you want to compare FROM (the reference database)." 
                                   Style="{StaticResource HelpTextStyle}" Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="Server:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="SourceServerTextBox" Grid.Row="0" Grid.Column="1" Margin="0,0,8,8"
                                     Style="{StaticResource MaterialTextBoxStyle}"
                                     ToolTip="Enter the Oracle server hostname or IP address (e.g., localhost, *************, or oracle.company.com)"
                                     ToolTipService.ToolTip="{Binding RelativeSource={RelativeSource Self}, Path=ToolTip}"
                                     TextChanged="SourceServerTextBox_TextChanged"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Port:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="SourcePortTextBox" Grid.Row="1" Grid.Column="1" Text="1521" Margin="0,0,8,8"
                                     Style="{StaticResource MaterialTextBoxStyle}"
                                     ToolTip="Oracle database port number (default is 1521)"
                                     TextChanged="SourcePortTextBox_TextChanged"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="Service:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="SourceServiceTextBox" Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="2" Margin="0,0,0,8"
                                     Style="{StaticResource MaterialTextBoxStyle}"
                                     ToolTip="Oracle service name or SID (e.g., ORCL, XE, or your custom service name)"
                                     TextChanged="SourceServiceTextBox_TextChanged"/>
                            
                            <TextBlock Grid.Row="3" Grid.Column="0" Text="Username:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="SourceUsernameTextBox" Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="2" Margin="0,0,0,8"
                                     Style="{StaticResource MaterialTextBoxStyle}"
                                     ToolTip="Database username with sufficient privileges to read schema information"
                                     TextChanged="SourceUsernameTextBox_TextChanged"/>
                            
                            <TextBlock Grid.Row="4" Grid.Column="0" Text="Password:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <PasswordBox x:Name="SourcePasswordBox" Grid.Row="4" Grid.Column="1" Margin="0,0,8,8"
                                         Style="{StaticResource MaterialPasswordBoxStyle}"
                                         ToolTip="Database password for the specified username"
                                         PasswordChanged="SourcePasswordBox_PasswordChanged"/>
                            <Button x:Name="TestSourceButton" Grid.Row="4" Grid.Column="2" Content="Test Connection" 
                                    Style="{StaticResource PrimaryButtonStyle}" Click="TestSourceButton_Click"
                                    ToolTip="Click to test the source database connection"/>
                            
                            <TextBlock Grid.Row="5" Grid.Column="0" Grid.ColumnSpan="3" x:Name="SourceConnectionStatus" 
                                       Style="{StaticResource HelpTextStyle}" Margin="0,8,0,0" Visibility="Collapsed"/>
                        </Grid>
                    </StackPanel>

                    <!-- Separator -->
                    <Border Grid.Column="1" Width="1" Background="{StaticResource BorderBrush}" Margin="16,0"/>

                    <!-- Target Database -->
                    <StackPanel Grid.Column="2">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Target Database" Style="{StaticResource HeaderTextStyle}"/>
                            <Border Grid.Column="1" x:Name="TargetStatusIndicator" Width="12" Height="12" 
                                    CornerRadius="6" Background="{StaticResource ProgressBackgroundBrush}" 
                                    ToolTip="Connection Status" Margin="8,0,0,0"/>
                        </Grid>
                        
                        <TextBlock Text="This is the database you want to compare TO (the database being checked for differences)." 
                                   Style="{StaticResource HelpTextStyle}" Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="Server:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="TargetServerTextBox" Grid.Row="0" Grid.Column="1" Margin="0,0,8,8"
                                     Style="{StaticResource MaterialTextBoxStyle}"
                                     ToolTip="Enter the Oracle server hostname or IP address for the target database"
                                     TextChanged="TargetServerTextBox_TextChanged"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Port:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="TargetPortTextBox" Grid.Row="1" Grid.Column="1" Text="1521" Margin="0,0,8,8"
                                     Style="{StaticResource MaterialTextBoxStyle}"
                                     ToolTip="Oracle database port number (default is 1521)"
                                     TextChanged="TargetPortTextBox_TextChanged"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="Service:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="TargetServiceTextBox" Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="2" Margin="0,0,0,8"
                                     Style="{StaticResource MaterialTextBoxStyle}"
                                     ToolTip="Oracle service name or SID for the target database"
                                     TextChanged="TargetServiceTextBox_TextChanged"/>
                            
                            <TextBlock Grid.Row="3" Grid.Column="0" Text="Username:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="TargetUsernameTextBox" Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="2" Margin="0,0,0,8"
                                     Style="{StaticResource MaterialTextBoxStyle}"
                                     ToolTip="Database username with sufficient privileges to read schema information"
                                     TextChanged="TargetUsernameTextBox_TextChanged"/>
                            
                            <TextBlock Grid.Row="4" Grid.Column="0" Text="Password:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <PasswordBox x:Name="TargetPasswordBox" Grid.Row="4" Grid.Column="1" Margin="0,0,8,8"
                                         Style="{StaticResource MaterialPasswordBoxStyle}"
                                         ToolTip="Database password for the specified username"
                                         PasswordChanged="TargetPasswordBox_PasswordChanged"/>
                            <Button x:Name="TestTargetButton" Grid.Row="4" Grid.Column="2" Content="Test Connection" 
                                    Style="{StaticResource PrimaryButtonStyle}" Click="TestTargetButton_Click"
                                    ToolTip="Click to test the target database connection"/>
                            
                            <TextBlock Grid.Row="5" Grid.Column="0" Grid.ColumnSpan="3" x:Name="TargetConnectionStatus" 
                                       Style="{StaticResource HelpTextStyle}" Margin="0,8,0,0" Visibility="Collapsed"/>
                        </Grid>
                    </StackPanel>

                    <!-- Compare Button Section -->
                    <StackPanel Grid.Column="3" VerticalAlignment="Center" Margin="16,0,0,0">
                        <Border Style="{StaticResource StepIndicatorStyle}" Margin="0,0,0,12">
                            <TextBlock Text="2" FontWeight="Bold" FontSize="14" 
                                       Foreground="{StaticResource SecondaryTextBrush}" 
                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        
                        <TextBlock Text="Start Comparison" FontWeight="SemiBold" FontSize="14" 
                                   HorizontalAlignment="Center" Margin="0,0,0,8"
                                   Foreground="{StaticResource PrimaryTextBrush}"/>
                        
                        <Button x:Name="CompareButton" Content="Compare Schemas" 
                                Style="{StaticResource PrimaryButtonStyle}" 
                                FontSize="16" Padding="20,12" IsEnabled="False"
                                Click="CompareButton_Click"
                                ToolTip="Click to start comparing the database schemas. Both connections must be tested successfully first."/>
                        
                        <StackPanel x:Name="CompareStatusPanel" Orientation="Horizontal" 
                                    HorizontalAlignment="Center" Margin="0,8,0,0" Visibility="Collapsed">
                            <Border x:Name="LoadingSpinner" Style="{StaticResource LoadingSpinnerStyle}" 
                                    Margin="0,0,8,0" Visibility="Collapsed"/>
                            <TextBlock x:Name="StatusTextBlock" Text="Ready to compare" 
                                       Foreground="{StaticResource SecondaryTextBrush}"/>
                        </StackPanel>
                        
                        <TextBlock Text="Ensure both database connections are tested and working before starting the comparison." 
                                   Style="{StaticResource HelpTextStyle}" TextAlignment="Center" 
                                   MaxWidth="150" Margin="0,8,0,0"/>
                    </StackPanel>
                </Grid>
                    </Grid>
                </Border>
            </TabItem>
            
            <!-- Step 2: Start Comparison -->
            <TabItem x:Name="ComparisonTab" Style="{StaticResource MaterialTabItemStyle}" IsEnabled="False">
                <TabItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <Border x:Name="Step2Indicator" Style="{StaticResource StepIndicatorStyle}" Margin="0,0,8,0" Width="24" Height="24">
                            <TextBlock Text="2" FontWeight="Bold" FontSize="12" 
                                       Foreground="{StaticResource SecondaryTextBrush}" 
                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock Text="Start Comparison" VerticalAlignment="Center"/>
                    </StackPanel>
                </TabItem.Header>
                
                <Border Style="{StaticResource CardStyle}" Margin="0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <!-- Step 2 Header -->
                        <StackPanel Grid.Row="0" Margin="0,0,0,24">
                            <TextBlock Text="Start Schema Comparison" Style="{StaticResource SectionHeaderStyle}"/>
                            <TextBlock Text="Both database connections are ready. Click the button below to start comparing the database schemas. This process will analyze all database objects and identify differences." 
                                       Style="{StaticResource InstructionTextStyle}"/>
                        </StackPanel>
                        
                        <!-- Comparison Controls -->
                        <Grid Grid.Row="1">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" MaxWidth="400">
                                <TextBlock Text="🔄" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,16"/>
                                
                                <TextBlock Text="Ready to Compare" FontWeight="SemiBold" FontSize="18" 
                                           HorizontalAlignment="Center" Margin="0,0,0,8"
                                           Foreground="{StaticResource PrimaryTextBrush}"/>
                                
                                <TextBlock Text="The comparison will analyze tables, views, procedures, functions, and other database objects to identify differences between the source and target databases." 
                                           Style="{StaticResource InstructionTextStyle}" TextAlignment="Center" Margin="0,0,0,24"/>
                                
                                <Button x:Name="StartCompareButton" Content="Compare Schemas" 
                                        Style="{StaticResource PrimaryButtonStyle}" 
                                        FontSize="16" Padding="32,16" IsEnabled="False"
                                        Click="CompareButton_Click"
                                        ToolTip="Click to start comparing the database schemas. Both connections must be tested successfully first."/>
                                
                                <StackPanel x:Name="StartCompareStatusPanel" Orientation="Horizontal" 
                                            HorizontalAlignment="Center" Margin="0,16,0,0" Visibility="Collapsed">
                                    <Border x:Name="StartCompareSpinner" Style="{StaticResource LoadingSpinnerStyle}" 
                                            Margin="0,0,8,0" Visibility="Collapsed"/>
                                    <TextBlock x:Name="StartCompareStatusText" Text="Ready to compare" 
                                               Foreground="{StaticResource SecondaryTextBrush}"/>
                                </StackPanel>
                                
                                <!-- Progress Bar -->
                                <ProgressBar x:Name="StartCompareProgressBar" 
                                             Style="{StaticResource MaterialProgressBarStyle}" 
                                             Margin="0,16,0,0" Visibility="Collapsed"/>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </Border>
            </TabItem>
            
            <!-- Step 3: Review Results -->
            <TabItem x:Name="ResultsTab" Style="{StaticResource MaterialTabItemStyle}" IsEnabled="False">
                <TabItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <Border x:Name="Step3Indicator" Style="{StaticResource StepIndicatorStyle}" Margin="0,0,8,0" Width="24" Height="24">
                            <TextBlock Text="3" FontWeight="Bold" FontSize="12" 
                                       Foreground="{StaticResource SecondaryTextBrush}" 
                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock Text="Review Results" VerticalAlignment="Center"/>
                    </StackPanel>
                </TabItem.Header>
                
                <Border Style="{StaticResource CardStyle}" Margin="0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <!-- Step 3 Header -->
                        <StackPanel Grid.Row="0" Margin="0,0,0,16">
                            <TextBlock Text="Review Schema Objects" Style="{StaticResource SectionHeaderStyle}"/>
                            <TextBlock Text="Browse and filter the comparison results. Click on any object to view detailed differences in the right panel." 
                                       Style="{StaticResource InstructionTextStyle}"/>
                        </StackPanel>
                        
                        <!-- Main Content Grid -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="5"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
            
                            <!-- Schema Objects Panel -->
                            <Border Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>
                                    
                                    <!-- Filter Panel -->
                                    <Border Grid.Row="0" Background="{StaticResource SecondaryBackgroundBrush}" 
                            Padding="12" Margin="0,0,0,8">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Text="Filter Results" FontWeight="SemiBold" FontSize="13" 
                                       Foreground="{StaticResource PrimaryTextBrush}" Margin="0,0,0,8"/>
                            
                            <Grid Grid.Row="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" Text="Schema Filter:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBox x:Name="SchemaFilterTextBox" Grid.Column="1" Margin="0,0,16,0"
                                         Style="{StaticResource MaterialTextBoxStyle}"
                                         ToolTip="Filter objects by schema name (e.g., HR, SALES). Leave empty to show all schemas."/>
                                
                                <TextBlock Grid.Column="2" Text="Object Type:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <ComboBox x:Name="ObjectTypeFilterComboBox" Grid.Column="3" Margin="0,0,16,0"
                                          Style="{StaticResource MaterialComboBoxStyle}"
                                          ItemContainerStyle="{StaticResource MaterialComboBoxItemStyle}"
                                          ToolTip="Filter by database object type (Tables, Views, Procedures, etc.)"/>
                                
                                <Button x:Name="ApplyFilterButton" Grid.Column="4" Content="Apply Filter" 
                                        Style="{StaticResource PrimaryButtonStyle}" Click="ApplyFilterButton_Click"
                                        ToolTip="Apply the selected filters to the results"/>
                            </Grid>
                        </Grid>
                    </Border>
                    
                                    <!-- Results Summary -->
                                    <Border Grid.Row="1" Background="#F8F9FA" Padding="12" Margin="0,0,0,8" 
                            x:Name="ResultsSummaryPanel" Visibility="Collapsed">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                <TextBlock x:Name="IdenticalCountText" Text="0" FontSize="18" FontWeight="Bold" 
                                           Foreground="{StaticResource SuccessBrush}" HorizontalAlignment="Center"/>
                                <TextBlock Text="Identical" FontSize="11" Foreground="{StaticResource HelpTextBrush}" 
                                           HorizontalAlignment="Center"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                <TextBlock x:Name="DifferentCountText" Text="0" FontSize="18" FontWeight="Bold" 
                                           Foreground="{StaticResource WarningBrush}" HorizontalAlignment="Center"/>
                                <TextBlock Text="Different" FontSize="11" Foreground="{StaticResource HelpTextBrush}" 
                                           HorizontalAlignment="Center"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                <TextBlock x:Name="SourceOnlyCountText" Text="0" FontSize="18" FontWeight="Bold" 
                                           Foreground="{StaticResource InfoBrush}" HorizontalAlignment="Center"/>
                                <TextBlock Text="Source Only" FontSize="11" Foreground="{StaticResource HelpTextBrush}" 
                                           HorizontalAlignment="Center"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                                <TextBlock x:Name="TargetOnlyCountText" Text="0" FontSize="18" FontWeight="Bold" 
                                           Foreground="{StaticResource ErrorBrush}" HorizontalAlignment="Center"/>
                                <TextBlock Text="Target Only" FontSize="11" Foreground="{StaticResource HelpTextBrush}" 
                                           HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                    
                    <!-- Schema Objects Grid -->
                    <DataGrid x:Name="SchemaObjectsGrid" Grid.Row="2"
                              AutoGenerateColumns="False"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              IsReadOnly="True"
                              SelectionMode="Single"
                              SelectionChanged="SchemaObjectsGrid_SelectionChanged"
                              Background="White"
                              Foreground="#212121"
                              FontFamily="Segoe UI"
                              FontSize="14"
                              RowHeight="48"
                              ColumnHeaderHeight="56"
                              GridLinesVisibility="Horizontal"
                              HeadersVisibility="Column"
                              BorderThickness="1"
                              BorderBrush="#E0E0E0">
                        <DataGrid.Resources>
                            <Style TargetType="{x:Type DataGridColumnHeader}">
                                <Setter Property="Background" Value="#1565C0"/>
                                <Setter Property="Foreground" Value="White"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                                <Setter Property="FontSize" Value="14"/>
                                <Setter Property="HorizontalContentAlignment" Value="Left"/>
                                <Setter Property="Padding" Value="16,12"/>
                                <Setter Property="BorderThickness" Value="0,0,1,0"/>
                                <Setter Property="BorderBrush" Value="#0D47A1"/>
                                <Setter Property="Height" Value="56"/>
                            </Style>
                            <Style TargetType="{x:Type DataGridRow}">
                                <Setter Property="Background" Value="Transparent"/>
                                <Setter Property="Height" Value="48"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#E3F2FD"/>
                                    </Trigger>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="#BBDEFB"/>
                                        <Setter Property="Foreground" Value="#0D47A1"/>
                                    </Trigger>
                                    <MultiTrigger>
                                        <MultiTrigger.Conditions>
                                            <Condition Property="IsSelected" Value="True"/>
                                            <Condition Property="IsMouseOver" Value="True"/>
                                        </MultiTrigger.Conditions>
                                        <Setter Property="Background" Value="#90CAF9"/>
                                    </MultiTrigger>
                                </Style.Triggers>
                            </Style>
                            <Style TargetType="{x:Type DataGridCell}">
                                <Setter Property="BorderThickness" Value="0"/>
                                <Setter Property="Padding" Value="16,12"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="{x:Type DataGridCell}">
                                            <Border Background="{TemplateBinding Background}" 
                                                    BorderBrush="{TemplateBinding BorderBrush}" 
                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                                <Style.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Setter Property="Foreground" Value="#0D47A1"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </DataGrid.Resources>
                        <DataGrid.Columns>
                            <DataGridTemplateColumn Header="Status" Width="120" IsReadOnly="True">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border CornerRadius="12" 
                                                Padding="8,4" 
                                                HorizontalAlignment="Left"
                                                VerticalAlignment="Center">
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Status}" Value="Identical">
                                                            <Setter Property="Background" Value="#E8F5E8"/>
                                                            <Setter Property="BorderBrush" Value="#4CAF50"/>
                                                            <Setter Property="BorderThickness" Value="1"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="Different">
                                                            <Setter Property="Background" Value="#FFF3E0"/>
                                                            <Setter Property="BorderBrush" Value="#FF9800"/>
                                                            <Setter Property="BorderThickness" Value="1"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="SourceOnly">
                                                            <Setter Property="Background" Value="#E3F2FD"/>
                                                            <Setter Property="BorderBrush" Value="#2196F3"/>
                                                            <Setter Property="BorderThickness" Value="1"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="TargetOnly">
                                                            <Setter Property="Background" Value="#FFEBEE"/>
                                                            <Setter Property="BorderBrush" Value="#F44336"/>
                                                            <Setter Property="BorderThickness" Value="1"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                            <TextBlock Text="{Binding Status}" 
                                                       FontSize="12" 
                                                       FontWeight="Medium"
                                                       VerticalAlignment="Center">
                                                <TextBlock.Style>
                                                    <Style TargetType="TextBlock">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding Status}" Value="Identical">
                                                                <Setter Property="Foreground" Value="#2E7D32"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Status}" Value="Different">
                                                                <Setter Property="Foreground" Value="#E65100"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Status}" Value="SourceOnly">
                                                                <Setter Property="Foreground" Value="#1565C0"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Status}" Value="TargetOnly">
                                                                <Setter Property="Foreground" Value="#C62828"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTextColumn Binding="{Binding Owner}" Header="Owner" Width="120" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Padding" Value="0"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Binding="{Binding Name}" Header="Object Name" Width="200" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Padding" Value="0"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="FontWeight" Value="Medium"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Binding="{Binding ObjectType}" Header="Type" Width="100" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Padding" Value="0"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="FontWeight" Value="Medium"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                                </Grid>
                            </Border>
                            
                            <!-- Splitter -->
                            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" Background="{StaticResource BorderBrush}"/>
                            
                            <!-- Details Panel -->
                            <Border Grid.Column="2" Style="{StaticResource CardStyle}" Margin="0">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>
                                    
                                    <!-- Empty State Message -->
                                    <Border Grid.Row="0" x:Name="EmptyStatePanel" Background="#F8F9FA" 
                            Padding="24" Margin="0,0,0,16" CornerRadius="6">
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                            <TextBlock Text="📋" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                            <TextBlock Text="No Object Selected" FontWeight="SemiBold" FontSize="16" 
                                       HorizontalAlignment="Center" Foreground="{StaticResource PrimaryTextBrush}"/>
                            <TextBlock Text="Click on any object in the left panel to view its detailed comparison results here." 
                                       Style="{StaticResource HelpTextStyle}" TextAlignment="Center" 
                                       MaxWidth="250" Margin="0,4,0,0"/>
                        </StackPanel>
                    </Border>
                    
                                    <TabControl x:Name="DetailsTabControl" Grid.Row="1" 
                                Style="{StaticResource MaterialTabControlStyle}">
                        <TabItem Header="Columns" x:Name="ColumnsTab" 
                                 Style="{StaticResource MaterialTabItemStyle}">
                            <DataGrid x:Name="ColumnsGrid"
                                      AutoGenerateColumns="False"
                                      CanUserAddRows="False"
                                      CanUserDeleteRows="False"
                                      IsReadOnly="True"
                                      Background="White"
                                      Foreground="#212121"
                                      FontFamily="Segoe UI"
                                      FontSize="14"
                                      RowHeight="48"
                                      ColumnHeaderHeight="56"
                                      GridLinesVisibility="Horizontal"
                                      HeadersVisibility="Column"
                                      BorderThickness="1"
                                      BorderBrush="#E0E0E0">
                                <DataGrid.Resources>
                                    <Style TargetType="{x:Type DataGridColumnHeader}">
                                        <Setter Property="Background" Value="#1565C0"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="FontSize" Value="14"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Left"/>
                                        <Setter Property="Padding" Value="16,12"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,0"/>
                                        <Setter Property="BorderBrush" Value="#0D47A1"/>
                                        <Setter Property="Height" Value="56"/>
                                    </Style>
                                    <Style TargetType="{x:Type DataGridRow}">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Setter Property="Height" Value="48"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#E3F2FD"/>
                                            </Trigger>
                                            <Trigger Property="IsSelected" Value="True">
                                                <Setter Property="Background" Value="#BBDEFB"/>
                                                <Setter Property="Foreground" Value="#0D47A1"/>
                                            </Trigger>
                                            <MultiTrigger>
                                                <MultiTrigger.Conditions>
                                                    <Condition Property="IsSelected" Value="True"/>
                                                    <Condition Property="IsMouseOver" Value="True"/>
                                                </MultiTrigger.Conditions>
                                                <Setter Property="Background" Value="#90CAF9"/>
                                            </MultiTrigger>
                                        </Style.Triggers>
                                    </Style>
                                    <Style TargetType="{x:Type DataGridCell}">
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="Padding" Value="16,12"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="{x:Type DataGridCell}">
                                                    <Border Background="{TemplateBinding Background}" 
                                                            BorderBrush="{TemplateBinding BorderBrush}" 
                                                            BorderThickness="{TemplateBinding BorderThickness}"
                                                            Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter VerticalAlignment="Center"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                        <Style.Triggers>
                                            <Trigger Property="IsSelected" Value="True">
                                                <Setter Property="Background" Value="Transparent"/>
                                                <Setter Property="Foreground" Value="#0D47A1"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                    <Style TargetType="{x:Type CheckBox}">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="IsEnabled" Value="False"/>
                                    </Style>
                                </DataGrid.Resources>
                                <DataGrid.Columns>
                                    <DataGridTemplateColumn Header="Status" Width="100" IsReadOnly="True">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <Border CornerRadius="12" 
                                                        Padding="6,3" 
                                                        HorizontalAlignment="Left"
                                                        VerticalAlignment="Center">
                                                    <Border.Style>
                                                        <Style TargetType="Border">
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding Status}" Value="Identical">
                                                                    <Setter Property="Background" Value="#E8F5E8"/>
                                                                    <Setter Property="BorderBrush" Value="#4CAF50"/>
                                                                    <Setter Property="BorderThickness" Value="1"/>
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding Status}" Value="Different">
                                                                    <Setter Property="Background" Value="#FFF3E0"/>
                                                                    <Setter Property="BorderBrush" Value="#FF9800"/>
                                                                    <Setter Property="BorderThickness" Value="1"/>
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding Status}" Value="SourceOnly">
                                                                    <Setter Property="Background" Value="#E3F2FD"/>
                                                                    <Setter Property="BorderBrush" Value="#2196F3"/>
                                                                    <Setter Property="BorderThickness" Value="1"/>
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding Status}" Value="TargetOnly">
                                                                    <Setter Property="Background" Value="#FFEBEE"/>
                                                                    <Setter Property="BorderBrush" Value="#F44336"/>
                                                                    <Setter Property="BorderThickness" Value="1"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </Border.Style>
                                                    <TextBlock Text="{Binding Status}" 
                                                               FontSize="11" 
                                                               FontWeight="Medium"
                                                               VerticalAlignment="Center">
                                                        <TextBlock.Style>
                                                            <Style TargetType="TextBlock">
                                                                <Style.Triggers>
                                                                    <DataTrigger Binding="{Binding Status}" Value="Identical">
                                                                        <Setter Property="Foreground" Value="#2E7D32"/>
                                                                    </DataTrigger>
                                                                    <DataTrigger Binding="{Binding Status}" Value="Different">
                                                                        <Setter Property="Foreground" Value="#E65100"/>
                                                                    </DataTrigger>
                                                                    <DataTrigger Binding="{Binding Status}" Value="SourceOnly">
                                                                        <Setter Property="Foreground" Value="#1565C0"/>
                                                                    </DataTrigger>
                                                                    <DataTrigger Binding="{Binding Status}" Value="TargetOnly">
                                                                        <Setter Property="Foreground" Value="#C62828"/>
                                                                    </DataTrigger>
                                                                </Style.Triggers>
                                                            </Style>
                                                        </TextBlock.Style>
                                                    </TextBlock>
                                                </Border>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                    <DataGridTextColumn Binding="{Binding ColumnName}" Header="Column Name" Width="150" IsReadOnly="True">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Padding" Value="0"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="FontWeight" Value="Medium"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Binding="{Binding FormattedDataType}" Header="Data Type" Width="120" IsReadOnly="True">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Padding" Value="0"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="FontFamily" Value="Consolas"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridCheckBoxColumn Binding="{Binding Nullable}" Header="Nullable" Width="80" IsReadOnly="True"/>
                                    <DataGridTextColumn Binding="{Binding DefaultValue}" Header="Default Value" Width="120" IsReadOnly="True">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Padding" Value="0"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="FontFamily" Value="Consolas"/>
                                                <Setter Property="FontStyle" Value="Italic"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridCheckBoxColumn Binding="{Binding IsPrimaryKey}" Header="PK" Width="50" IsReadOnly="True"/>
                                    <DataGridCheckBoxColumn Binding="{Binding IsForeignKey}" Header="FK" Width="50" IsReadOnly="True"/>
                                    <DataGridTextColumn Binding="{Binding Comments}" Header="Comments" Width="200" IsReadOnly="True">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Padding" Value="0"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="TextWrapping" Value="Wrap"/>
                                                <Setter Property="FontStyle" Value="Italic"/>
                                                <Setter Property="Foreground" Value="#666"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </TabItem>
                        
                        <TabItem Header="Definition" x:Name="DefinitionTab" 
                                 Style="{StaticResource MaterialTabItemStyle}">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Text="Source Definition" 
                                           FontFamily="Segoe UI" FontSize="14" FontWeight="SemiBold" 
                                           Foreground="{StaticResource PrimaryTextBrush}" Margin="0,0,0,8"/>
                                <TextBox x:Name="SourceDefinitionEditor" Grid.Row="1"
                                         Style="{StaticResource MaterialCodeEditorStyle}"
                                         IsReadOnly="True"
                                         TextWrapping="Wrap"
                                         AcceptsReturn="True"
                                         VerticalScrollBarVisibility="Auto"
                                         HorizontalScrollBarVisibility="Auto"
                                         Margin="0,0,0,16"/>
                                
                                <TextBlock Grid.Row="2" Text="Target Definition" 
                                           FontFamily="Segoe UI" FontSize="14" FontWeight="SemiBold" 
                                           Foreground="{StaticResource PrimaryTextBrush}" Margin="0,0,0,8"/>
                                <TextBox x:Name="TargetDefinitionEditor" Grid.Row="3"
                                         Style="{StaticResource MaterialCodeEditorStyle}"
                                         IsReadOnly="True"
                                         TextWrapping="Wrap"
                                         AcceptsReturn="True"
                                         VerticalScrollBarVisibility="Auto"
                                         HorizontalScrollBarVisibility="Auto"/>
                            </Grid>
                        </TabItem>
                                    </TabControl>
                                </Grid>
                            </Border>
                        </Grid>
                    </Grid>
                </Border>
            </TabItem>
        </TabControl>

        <!-- Enhanced Status Bar -->
        <Border Grid.Row="3" Background="{StaticResource SecondaryBackgroundBrush}" 
                BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,1,0,0" Padding="16,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- Statistics Panel -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Border Background="White" CornerRadius="4" Padding="8,4" Margin="0,0,12,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📊" FontSize="14" Margin="0,0,4,0"/>
                            <TextBlock x:Name="ObjectCountTextBlock" Text="Objects: 0" FontWeight="Medium"/>
                        </StackPanel>
                    </Border>
                    
                    <Border Background="#E8F5E8" CornerRadius="4" Padding="8,4" Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="✅" FontSize="12" Margin="0,0,4,0"/>
                            <TextBlock x:Name="IdenticalCountTextBlock" Text="Identical: 0" 
                                       Foreground="{StaticResource SuccessBrush}" FontWeight="Medium"/>
                        </StackPanel>
                    </Border>
                    
                    <Border Background="#FFF3E0" CornerRadius="4" Padding="8,4" Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="⚠️" FontSize="12" Margin="0,0,4,0"/>
                            <TextBlock x:Name="DifferentCountTextBlock" Text="Different: 0" 
                                       Foreground="{StaticResource WarningBrush}" FontWeight="Medium"/>
                        </StackPanel>
                    </Border>
                    
                    <Border Background="#E3F2FD" CornerRadius="4" Padding="8,4" Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📤" FontSize="12" Margin="0,0,4,0"/>
                            <TextBlock x:Name="SourceOnlyCountTextBlock" Text="Source Only: 0" 
                                       Foreground="{StaticResource InfoBrush}" FontWeight="Medium"/>
                        </StackPanel>
                    </Border>
                    
                    <Border Background="#FFEBEE" CornerRadius="4" Padding="8,4">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📥" FontSize="12" Margin="0,0,4,0"/>
                            <TextBlock x:Name="TargetOnlyCountTextBlock" Text="Target Only: 0" 
                                       Foreground="{StaticResource ErrorBrush}" FontWeight="Medium"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
                
                <!-- Progress Section -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="16,0">
                    <Border x:Name="ProgressContainer" Background="White" CornerRadius="4" 
                            Padding="12,6" Visibility="Collapsed">
                        <StackPanel Orientation="Horizontal">
                            <Border x:Name="StatusSpinner" Style="{StaticResource LoadingSpinnerStyle}" 
                                    Margin="0,0,8,0" Visibility="Collapsed"/>
                            <ProgressBar x:Name="ProgressBar" Width="150" Height="4" 
                                         Style="{StaticResource MaterialProgressBarStyle}"/>
                            <TextBlock x:Name="ProgressPercentage" Text="0%" 
                                       FontSize="12" FontWeight="Medium" Margin="8,0,0,0"
                                       Foreground="{StaticResource SecondaryTextBrush}"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
                
                <!-- Status Message -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Border Background="White" CornerRadius="4" Padding="12,6">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock x:Name="StatusIcon" Text="🔄" FontSize="14" Margin="0,0,6,0"/>
                            <TextBlock x:Name="StatusBarTextBlock" Text="Ready to start - Configure database connections" 
                                       FontWeight="Medium" Foreground="{StaticResource SecondaryTextBrush}"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>