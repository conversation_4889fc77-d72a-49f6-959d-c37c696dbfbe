using System.Configuration;
using System.Data;
using System.Windows;
using Serilog;
using Telerik.Windows.Controls;

namespace DBHarmonizer
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            // Configure Serilog
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .WriteTo.Console()
                .WriteTo.File("logs/dbharmonizer-.txt", rollingInterval: RollingInterval.Day)
                .CreateLogger();

            Log.Information("DBHarmonizer application starting...");

            // Set Telerik theme
            StyleManager.ApplicationTheme = new MaterialTheme();

            base.OnStartup(e);
        }

        protected override void OnExit(ExitEventArgs e)
        {
            Log.Information("DBHarmonizer application shutting down...");
            Log.CloseAndFlush();
            base.OnExit(e);
        }
    }
}
