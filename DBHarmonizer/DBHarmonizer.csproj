<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Oracle.ManagedDataAccess.Core" Version="23.6.0" />
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="Telerik.UI.for.Wpf.AllControls.Xaml" Version="2025.1.211" />
    <PackageReference Include="Telerik.Windows.Themes.Material.for.Wpf" Version="2025.1.211" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="8.0.1" />
  </ItemGroup>

</Project>
