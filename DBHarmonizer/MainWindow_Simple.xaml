<Window x:Class="DBHarmonizer.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:DBHarmonizer"
        xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
        mc:Ignorable="d"
        Title="Oracle Database Schema Structure Compare" 
        Height="900" Width="1400"
        MinHeight="700" MinWidth="1200"
        Background="#F8FAFC"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized">
    
    <Window.Resources>
        <!-- Status to Color Converter -->
        <local:ComparisonStatusToColorConverter x:Key="StatusToColorConverter"/>
        <local:ComparisonStatusToIconConverter x:Key="StatusToIconConverter"/>
        
        <!-- Navy Blue Color Palette -->
        <SolidColorBrush x:Key="PrimaryNavyBrush" Color="#1E3A8A"/>
        <SolidColorBrush x:Key="SecondaryNavyBrush" Color="#3B82F6"/>
        <SolidColorBrush x:Key="LightNavyBrush" Color="#60A5FA"/>
        <SolidColorBrush x:Key="DarkNavyBrush" Color="#1E40AF"/>
        <SolidColorBrush x:Key="AccentNavyBrush" Color="#2563EB"/>
        
        <!-- Background Colors -->
        <SolidColorBrush x:Key="MainBackgroundBrush" Color="#F8FAFC"/>
        <SolidColorBrush x:Key="SecondaryBackgroundBrush" Color="#F1F5F9"/>
        <SolidColorBrush x:Key="CardBackgroundBrush" Color="#FFFFFF"/>
        
        <!-- Text Colors -->
        <SolidColorBrush x:Key="PrimaryTextBrush" Color="#0F172A"/>
        <SolidColorBrush x:Key="SecondaryTextBrush" Color="#475569"/>
        <SolidColorBrush x:Key="AccentTextBrush" Color="#1E3A8A"/>
        
        <!-- Border Colors -->
        <SolidColorBrush x:Key="BorderBrush" Color="#E2E8F0"/>
        <SolidColorBrush x:Key="FocusBorderBrush" Color="#3B82F6"/>
        
        <!-- Status Colors -->
        <SolidColorBrush x:Key="SuccessBrush" Color="#10B981"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#F59E0B"/>
        <SolidColorBrush x:Key="ErrorBrush" Color="#EF4444"/>
        <SolidColorBrush x:Key="InfoBrush" Color="#3B82F6"/>
        
        <!-- Card Style -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource CardBackgroundBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#E2E8F0" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Header Style -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryNavyBrush}"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
        </Style>
        
        <!-- Button Style -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryNavyBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="{StaticResource PrimaryNavyBrush}"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="1" 
                                CornerRadius="6" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryNavyBrush}" Padding="20,16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🗄️" FontSize="24" Foreground="White" Margin="0,0,12,0"/>
                    <TextBlock Text="Oracle Database Schema Structure Compare" 
                               FontSize="20" FontWeight="SemiBold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="SettingsButton" Content="Settings" Style="{StaticResource PrimaryButtonStyle}" 
                            Background="{StaticResource SecondaryNavyBrush}" Margin="0,0,8,0" Click="SettingsButton_Click"/>
                    <Button x:Name="AboutButton" Content="About" Style="{StaticResource PrimaryButtonStyle}" 
                            Background="{StaticResource SecondaryNavyBrush}" Click="AboutButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Connection Panel -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="16,16,16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Source Database -->
                <StackPanel Grid.Column="0">
                    <TextBlock Text="Source Database" Style="{StaticResource HeaderTextStyle}"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Server:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                        <TextBox x:Name="SourceServerTextBox" Grid.Row="0" Grid.Column="1" Margin="0,0,8,8"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Port:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                        <TextBox x:Name="SourcePortTextBox" Grid.Row="1" Grid.Column="1" Text="1521" Margin="0,0,8,8"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Service:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                        <TextBox x:Name="SourceServiceTextBox" Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="2" Margin="0,0,0,8"/>
                        
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="Username:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                        <TextBox x:Name="SourceUsernameTextBox" Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="2" Margin="0,0,0,8"/>
                        
                        <TextBlock Grid.Row="4" Grid.Column="0" Text="Password:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <PasswordBox x:Name="SourcePasswordBox" Grid.Row="4" Grid.Column="1" Margin="0,0,8,0"/>
                        <Button x:Name="TestSourceButton" Grid.Row="4" Grid.Column="2" Content="Test" 
                                Style="{StaticResource PrimaryButtonStyle}" Click="TestSourceButton_Click"/>
                    </Grid>
                </StackPanel>

                <!-- Separator -->
                <Border Grid.Column="1" Width="1" Background="{StaticResource BorderBrush}" Margin="16,0"/>

                <!-- Target Database -->
                <StackPanel Grid.Column="2">
                    <TextBlock Text="Target Database" Style="{StaticResource HeaderTextStyle}"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Server:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                        <TextBox x:Name="TargetServerTextBox" Grid.Row="0" Grid.Column="1" Margin="0,0,8,8"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Port:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                        <TextBox x:Name="TargetPortTextBox" Grid.Row="1" Grid.Column="1" Text="1521" Margin="0,0,8,8"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Service:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                        <TextBox x:Name="TargetServiceTextBox" Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="2" Margin="0,0,0,8"/>
                        
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="Username:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                        <TextBox x:Name="TargetUsernameTextBox" Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="2" Margin="0,0,0,8"/>
                        
                        <TextBlock Grid.Row="4" Grid.Column="0" Text="Password:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <PasswordBox x:Name="TargetPasswordBox" Grid.Row="4" Grid.Column="1" Margin="0,0,8,0"/>
                        <Button x:Name="TestTargetButton" Grid.Row="4" Grid.Column="2" Content="Test" 
                                Style="{StaticResource PrimaryButtonStyle}" Click="TestTargetButton_Click"/>
                    </Grid>
                </StackPanel>

                <!-- Compare Button -->
                <StackPanel Grid.Column="3" VerticalAlignment="Center" Margin="16,0,0,0">
                    <Button x:Name="CompareButton" Content="Compare Schemas" 
                            Style="{StaticResource PrimaryButtonStyle}" 
                            FontSize="16" Padding="20,12"
                            Click="CompareButton_Click"/>
                    <TextBlock x:Name="StatusTextBlock" Text="Ready to compare" 
                               HorizontalAlignment="Center" Margin="0,8,0,0"
                               Foreground="{StaticResource SecondaryTextBrush}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="2" Margin="16,8,16,8">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Schema Objects Panel -->
            <Border Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Text="Schema Objects" Style="{StaticResource HeaderTextStyle}"/>
                    
                    <!-- Filter Panel -->
                    <Border Grid.Row="1" Background="{StaticResource SecondaryBackgroundBrush}" 
                            Padding="12" Margin="0,0,0,8">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="Schema Filter:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBox x:Name="SchemaFilterTextBox" Grid.Column="1" Margin="0,0,16,0"/>
                            
                            <TextBlock Grid.Column="2" Text="Object Type:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <ComboBox x:Name="ObjectTypeFilterComboBox" Grid.Column="3" Margin="0,0,16,0"/>
                            
                            <Button x:Name="ApplyFilterButton" Grid.Column="4" Content="Apply Filter" 
                                    Style="{StaticResource PrimaryButtonStyle}" Click="ApplyFilterButton_Click"/>
                        </Grid>
                    </Border>
                    
                    <!-- Schema Objects Grid -->
                    <DataGrid x:Name="SchemaObjectsGrid" Grid.Row="2"
                              AutoGenerateColumns="False"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              IsReadOnly="True"
                              SelectionMode="Single"
                              SelectionChanged="SchemaObjectsGrid_SelectionChanged">
                        <DataGrid.Columns>
                            <DataGridTextColumn Binding="{Binding Status}" Header="Status" Width="80"/>
                            <DataGridTextColumn Binding="{Binding Owner}" Header="Owner" Width="120"/>
                            <DataGridTextColumn Binding="{Binding Name}" Header="Object Name" Width="200"/>
                            <DataGridTextColumn Binding="{Binding ObjectType}" Header="Type" Width="100"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </Border>
            
            <!-- Splitter -->
            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" Background="{StaticResource BorderBrush}"/>
            
            <!-- Details Panel -->
            <Border Grid.Column="2" Style="{StaticResource CardStyle}" Margin="0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Text="Object Details" Style="{StaticResource HeaderTextStyle}"/>
                    
                    <TabControl x:Name="DetailsTabControl" Grid.Row="1">
                        <TabItem Header="Columns" x:Name="ColumnsTab">
                            <DataGrid x:Name="ColumnsGrid"
                                      AutoGenerateColumns="False"
                                      CanUserAddRows="False"
                                      CanUserDeleteRows="False"
                                      IsReadOnly="True">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Binding="{Binding Status}" Header="Status" Width="80"/>
                                    <DataGridTextColumn Binding="{Binding ColumnName}" Header="Column Name" Width="150"/>
                                    <DataGridTextColumn Binding="{Binding FormattedDataType}" Header="Data Type" Width="120"/>
                                    <DataGridCheckBoxColumn Binding="{Binding Nullable}" Header="Nullable" Width="80"/>
                                    <DataGridTextColumn Binding="{Binding DefaultValue}" Header="Default Value" Width="120"/>
                                    <DataGridCheckBoxColumn Binding="{Binding IsPrimaryKey}" Header="PK" Width="50"/>
                                    <DataGridCheckBoxColumn Binding="{Binding IsForeignKey}" Header="FK" Width="50"/>
                                    <DataGridTextColumn Binding="{Binding Comments}" Header="Comments" Width="200"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </TabItem>
                        
                        <TabItem Header="Definition" x:Name="DefinitionTab">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Text="Source Definition" FontWeight="Bold" Margin="0,0,0,4"/>
                                <TextBox x:Name="SourceDefinitionEditor" Grid.Row="1"
                                         IsReadOnly="True"
                                         FontFamily="Consolas"
                                         FontSize="12"
                                         TextWrapping="Wrap"
                                         AcceptsReturn="True"
                                         VerticalScrollBarVisibility="Auto"
                                         HorizontalScrollBarVisibility="Auto"
                                         Margin="0,0,0,8"/>
                                
                                <TextBlock Grid.Row="2" Text="Target Definition" FontWeight="Bold" Margin="0,0,0,4"/>
                                <TextBox x:Name="TargetDefinitionEditor" Grid.Row="3"
                                         IsReadOnly="True"
                                         FontFamily="Consolas"
                                         FontSize="12"
                                         TextWrapping="Wrap"
                                         AcceptsReturn="True"
                                         VerticalScrollBarVisibility="Auto"
                                         HorizontalScrollBarVisibility="Auto"/>
                            </Grid>
                        </TabItem>
                    </TabControl>
                </Grid>
            </Border>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="3" Background="{StaticResource SecondaryBackgroundBrush}" 
                BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,1,0,0" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock x:Name="ObjectCountTextBlock" Text="Objects: 0" Margin="0,0,16,0"/>
                    <TextBlock x:Name="IdenticalCountTextBlock" Text="Identical: 0" Margin="0,0,16,0" 
                               Foreground="{StaticResource SuccessBrush}"/>
                    <TextBlock x:Name="DifferentCountTextBlock" Text="Different: 0" Margin="0,0,16,0" 
                               Foreground="{StaticResource WarningBrush}"/>
                    <TextBlock x:Name="SourceOnlyCountTextBlock" Text="Source Only: 0" Margin="0,0,16,0" 
                               Foreground="{StaticResource InfoBrush}"/>
                    <TextBlock x:Name="TargetOnlyCountTextBlock" Text="Target Only: 0" 
                               Foreground="{StaticResource ErrorBrush}"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <ProgressBar x:Name="ProgressBar" Width="200" Height="16" 
                                 Visibility="Collapsed" Margin="0,0,16,0"/>
                    <TextBlock x:Name="StatusBarTextBlock" Text="Ready" 
                               Foreground="{StaticResource SecondaryTextBrush}"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>