using System.ComponentModel;

namespace DBHarmonizer.Models
{
    public class TableColumn : INotifyPropertyChanged
    {
        private string _columnName = string.Empty;
        private string _dataType = string.Empty;
        private int? _dataLength;
        private int? _dataPrecision;
        private int? _dataScale;
        private bool _nullable = true;
        private string? _defaultValue;
        private bool _isPrimaryKey;
        private bool _isForeignKey;
        private string _comments = string.Empty;
        private ComparisonStatus _status;

        public string ColumnName
        {
            get => _columnName;
            set
            {
                _columnName = value;
                OnPropertyChanged(nameof(ColumnName));
            }
        }

        public string DataType
        {
            get => _dataType;
            set
            {
                _dataType = value;
                OnPropertyChanged(nameof(DataType));
            }
        }

        public int? DataLength
        {
            get => _dataLength;
            set
            {
                _dataLength = value;
                OnPropertyChanged(nameof(DataLength));
            }
        }

        public int? DataPrecision
        {
            get => _dataPrecision;
            set
            {
                _dataPrecision = value;
                OnPropertyChanged(nameof(DataPrecision));
            }
        }

        public int? DataScale
        {
            get => _dataScale;
            set
            {
                _dataScale = value;
                OnPropertyChanged(nameof(DataScale));
            }
        }

        public bool Nullable
        {
            get => _nullable;
            set
            {
                _nullable = value;
                OnPropertyChanged(nameof(Nullable));
            }
        }

        public string? DefaultValue
        {
            get => _defaultValue;
            set
            {
                _defaultValue = value;
                OnPropertyChanged(nameof(DefaultValue));
            }
        }

        public bool IsPrimaryKey
        {
            get => _isPrimaryKey;
            set
            {
                _isPrimaryKey = value;
                OnPropertyChanged(nameof(IsPrimaryKey));
            }
        }

        public bool IsForeignKey
        {
            get => _isForeignKey;
            set
            {
                _isForeignKey = value;
                OnPropertyChanged(nameof(IsForeignKey));
            }
        }

        public string Comments
        {
            get => _comments;
            set
            {
                _comments = value;
                OnPropertyChanged(nameof(Comments));
            }
        }

        public ComparisonStatus Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged(nameof(Status));
            }
        }

        public string FormattedDataType
        {
            get
            {
                var result = DataType;
                if (DataLength.HasValue && DataType.ToUpper() is "VARCHAR2" or "CHAR" or "NVARCHAR2" or "NCHAR")
                {
                    result += $"({DataLength})";
                }
                else if (DataPrecision.HasValue && DataScale.HasValue && DataType.ToUpper() == "NUMBER")
                {
                    result += $"({DataPrecision},{DataScale})";
                }
                else if (DataPrecision.HasValue && DataType.ToUpper() == "NUMBER")
                {
                    result += $"({DataPrecision})";
                }
                return result;
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}