using System.ComponentModel;

namespace DBHarmonizer.Models
{
    public enum SchemaObjectType
    {
        Table,
        View,
        Index,
        Sequence,
        Trigger,
        Procedure,
        Function,
        Package,
        Synonym,
        Constraint
    }

    public enum ComparisonStatus
    {
        Identical,
        Different,
        SourceOnly,
        TargetOnly
    }

    public class SchemaObject : INotifyPropertyChanged
    {
        private string _name = string.Empty;
        private string _owner = string.Empty;
        private SchemaObjectType _objectType;
        private string _definition = string.Empty;
        private ComparisonStatus _status;
        private string _differences = string.Empty;

        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged(nameof(Name));
            }
        }

        public string Owner
        {
            get => _owner;
            set
            {
                _owner = value;
                OnPropertyChanged(nameof(Owner));
            }
        }

        public SchemaObjectType ObjectType
        {
            get => _objectType;
            set
            {
                _objectType = value;
                OnPropertyChanged(nameof(ObjectType));
            }
        }

        public string Definition
        {
            get => _definition;
            set
            {
                _definition = value;
                OnPropertyChanged(nameof(Definition));
            }
        }

        public ComparisonStatus Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged(nameof(Status));
            }
        }

        public string Differences
        {
            get => _differences;
            set
            {
                _differences = value;
                OnPropertyChanged(nameof(Differences));
            }
        }

        public string FullName => $"{Owner}.{Name}";

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}