using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;
using DBHarmonizer.Models;

namespace DBHarmonizer
{
    public class ComparisonStatusToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ComparisonStatus status)
            {
                return status switch
                {
                    ComparisonStatus.Identical => new SolidColorBrush(Color.FromRgb(16, 185, 129)), // Green
                    ComparisonStatus.Different => new SolidColorBrush(Color.FromRgb(245, 158, 11)), // Orange
                    ComparisonStatus.SourceOnly => new SolidColorBrush(Color.FromRgb(59, 130, 246)), // Blue
                    ComparisonStatus.TargetOnly => new SolidColorBrush(Color.FromRgb(239, 68, 68)), // Red
                    _ => new SolidColorBrush(Color.FromRgb(107, 114, 128)) // Gray
                };
            }
            return new SolidColorBrush(Color.FromRgb(107, 114, 128));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class ComparisonStatusToIconConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ComparisonStatus status)
            {
                return status switch
                {
                    ComparisonStatus.Identical => "\ue930", // Check mark
                    ComparisonStatus.Different => "\ue7ba", // Warning
                    ComparisonStatus.SourceOnly => "\ue72a", // Arrow right
                    ComparisonStatus.TargetOnly => "\ue72b", // Arrow left
                    _ => "\ue946" // Question mark
                };
            }
            return "\ue946";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}