<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <appSettings>
        <!-- Application Settings -->
        <add key="DefaultPort" value="1521" />
        <add key="LogLevel" value="Information" />
        <add key="MaxLogFileSizeMB" value="10" />
        <add key="LogRetentionDays" value="30" />
        
        <!-- UI Settings -->
        <add key="DefaultWindowWidth" value="1400" />
        <add key="DefaultWindowHeight" value="900" />
        <add key="AutoSaveConnections" value="true" />
        
        <!-- Database Settings -->
        <add key="ConnectionTimeout" value="30" />
        <add key="CommandTimeout" value="300" />
        <add key="MaxObjectsToCompare" value="10000" />
    </appSettings>
    
    <connectionStrings>
        <!-- Sample connection strings - these will be overridden by user input -->
        <add name="SourceDatabase" 
             connectionString="Data Source=localhost:1521/XE;User Id=hr;Password=password;" 
             providerName="Oracle.ManagedDataAccess.Client" />
        <add name="TargetDatabase" 
             connectionString="Data Source=localhost:1521/XE;User Id=hr;Password=password;" 
             providerName="Oracle.ManagedDataAccess.Client" />
    </connectionStrings>
</configuration>