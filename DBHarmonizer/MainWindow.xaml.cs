using System.Collections.ObjectModel;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using DBHarmonizer.Models;
using DBHarmonizer.Services;
using Serilog;

namespace DBHarmonizer
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly OracleSchemaService _oracleService;
        private readonly SchemaComparisonService _comparisonService;
        private DatabaseConnection _sourceConnection;
        private DatabaseConnection _targetConnection;
        private ObservableCollection<SchemaObject> _schemaObjects;
        private ObservableCollection<Models.TableColumn> _tableColumns;

        public MainWindow()
        {
            InitializeComponent();
            
            _oracleService = new OracleSchemaService();
            _comparisonService = new SchemaComparisonService();
            _sourceConnection = new DatabaseConnection { Name = "Source" };
            _targetConnection = new DatabaseConnection { Name = "Target" };
            _schemaObjects = new ObservableCollection<SchemaObject>();
            _tableColumns = new ObservableCollection<Models.TableColumn>();

            InitializeUI();
        }

        private void InitializeUI()
        {
            // Initialize object type filter
            ObjectTypeFilterComboBox.ItemsSource = Enum.GetValues(typeof(SchemaObjectType));
            ObjectTypeFilterComboBox.SelectedIndex = -1;

            // Initialize TabControl - start with first tab
            MainTabControl.SelectedIndex = 0;
            UpdateTabStates();

            // Initialize enhanced UI/UX features
            InitializeEnhancedUI();

            // Set grid data sources
            SchemaObjectsGrid.ItemsSource = _schemaObjects;
            ColumnsGrid.ItemsSource = _tableColumns;

            // Set initial status
            UpdateStatusBar();
        }

        private void InitializeEnhancedUI()
        {
            // Initialize connection status indicators
            SourceStatusIndicator.Background = (SolidColorBrush)FindResource("ProgressBackgroundBrush");
            TargetStatusIndicator.Background = (SolidColorBrush)FindResource("ProgressBackgroundBrush");
            
            // Initialize button states
            CompareButton.IsEnabled = false;
            TestSourceButton.IsEnabled = false;
            TestTargetButton.IsEnabled = false;
            
            // Initialize progress elements
            ConnectionProgressBar.Visibility = Visibility.Collapsed;
            ProgressContainer.Visibility = Visibility.Collapsed;
            StatusSpinner.Visibility = Visibility.Collapsed;
            LoadingSpinner.Visibility = Visibility.Collapsed;
            
            // Initialize status messages
            SourceConnectionStatus.Visibility = Visibility.Collapsed;
            TargetConnectionStatus.Visibility = Visibility.Collapsed;
            ResultsSummaryPanel.Visibility = Visibility.Collapsed;
            
            // Initialize empty state
            EmptyStatePanel.Visibility = Visibility.Visible;
            
            // Set initial status bar message
            StatusBarTextBlock.Text = "Ready to start - Configure database connections";
            StatusIcon.Text = "🔄";
            
            // Initialize tooltips for enhanced user guidance
            InitializeTooltips();
        }

        private void InitializeTooltips()
        {
            // Enhanced tooltips are already set in XAML, but we can add dynamic ones here if needed
            CompareButton.ToolTip = "Click to start comparing the database schemas. Both connections must be tested successfully first.";
            
            // Add context-sensitive tooltips based on current state
            UpdateContextualTooltips();
        }

        private void UpdateContextualTooltips()
        {
            if (!_sourceConnection.IsConnected && !_targetConnection.IsConnected)
            {
                CompareButton.ToolTip = "Please test both database connections before comparing schemas.";
            }
            else if (_sourceConnection.IsConnected && _targetConnection.IsConnected)
            {
                CompareButton.ToolTip = "Both databases are connected. Click to start schema comparison.";
            }
            else
            {
                CompareButton.ToolTip = "Please test both database connections successfully before comparing.";
            }
        }

        #region Tab Management

        private void MainTabControl_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (MainTabControl.SelectedItem is TabItem selectedTab)
            {
                UpdateStatusBarForTab(selectedTab);
            }
        }

        private void UpdateTabStates()
        {
            // Enable/disable tabs based on current state
            bool connectionsReady = _sourceConnection.IsConnected && _targetConnection.IsConnected;
            bool comparisonComplete = _schemaObjects.Count > 0;

            // Step 2: Comparison tab - enabled when both connections are ready
            ComparisonTab.IsEnabled = connectionsReady;
            if (connectionsReady)
            {
                Step2Indicator.Style = (Style)FindResource("ActiveStepIndicatorStyle");
            }

            // Step 3: Results tab - enabled when comparison is complete
            ResultsTab.IsEnabled = comparisonComplete;
            if (comparisonComplete)
            {
                Step3Indicator.Style = (Style)FindResource("ActiveStepIndicatorStyle");
            }

            // Update Compare button state
            CompareButton.IsEnabled = connectionsReady;
        }

        private void UpdateStatusBarForTab(TabItem selectedTab)
        {
            if (selectedTab == ConnectionsTab)
            {
                UpdateStatusBarMessage("Configure database connections", "🔧");
            }
            else if (selectedTab == ComparisonTab)
            {
                UpdateStatusBarMessage("Ready to start schema comparison", "🔄");
            }
            else if (selectedTab == ResultsTab)
            {
                UpdateStatusBarMessage("Review comparison results", "📊");
            }
        }

        private void NavigateToTab(int tabIndex)
        {
            if (tabIndex >= 0 && tabIndex < MainTabControl.Items.Count)
            {
                var tab = MainTabControl.Items[tabIndex] as TabItem;
                if (tab != null && tab.IsEnabled)
                {
                    MainTabControl.SelectedIndex = tabIndex;
                }
            }
        }

        #endregion

        private async void TestSourceButton_Click(object sender, RoutedEventArgs e)
        {
            await TestConnection(_sourceConnection, "Source", TestSourceButton);
        }

        private async void TestTargetButton_Click(object sender, RoutedEventArgs e)
        {
            await TestConnection(_targetConnection, "Target", TestTargetButton);
        }

        private async Task TestConnection(DatabaseConnection connection, string connectionName, Button button)
        {
            try
            {
                // Show progress and update UI
                button.IsEnabled = false;
                button.Content = "Testing...";
                ShowProgress(true, $"Testing {connectionName} connection...");

                UpdateConnectionFromUI(connection, connectionName == "Source");

                var isConnected = await _oracleService.TestConnectionAsync(connection);
                connection.IsConnected = isConnected;

                if (isConnected)
                {
                    button.Content = "✓ Connected";
                    button.Background = (SolidColorBrush)FindResource("SuccessBrush");
                    ShowConnectionStatus(connectionName, true, $"✅ {connectionName} database connected successfully");
                    Log.Information("{ConnectionName} database connection successful", connectionName);
                    
                    // Update step 1 progress if both connections are successful
                    if (_sourceConnection.IsConnected && _targetConnection.IsConnected)
                    {
                        UpdateStep1ToCompleted();
                    }
                }
                else
                {
                    button.Content = "✗ Failed";
                    button.Background = (SolidColorBrush)FindResource("ErrorBrush");
                    ShowConnectionStatus(connectionName, false, $"❌ Failed to connect to {connectionName} database. Please verify your connection settings.");
                    MessageBox.Show($"Failed to connect to {connectionName} database.\n\nPlease check:\n• Server hostname/IP address\n• Port number (usually 1521)\n• Service name or SID\n• Username and password\n• Network connectivity", 
                                  "Connection Failed", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                button.Content = "✗ Error";
                button.Background = (SolidColorBrush)FindResource("ErrorBrush");
                ShowConnectionStatus(connectionName, false, $"⚠️ Connection error: {ex.Message}");
                Log.Error(ex, "Error testing {ConnectionName} connection", connectionName);
                MessageBox.Show($"Error testing {connectionName} connection:\n\n{ex.Message}\n\nPlease check your connection parameters and try again.", 
                              "Connection Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                ShowProgress(false);
                button.IsEnabled = true;
                UpdateCompareButtonState();
                UpdateStatusBarMessage();
                UpdateTabStates(); // Update tab states based on connection status
                
                // Reset button appearance after delay if not connected
                if (!connection.IsConnected)
                {
                    await Task.Delay(3000);
                    button.Content = "Test Connection";
                    button.Background = (SolidColorBrush)FindResource("PrimaryNavyBrush");
                }
            }
        }

        private void UpdateStep1ToCompleted()
        {
            // Find and update step 1 indicator to completed state
            try
            {
                var mainGrid = (Grid)this.Content;
                var connectionPanel = (Border)mainGrid.Children[1];
                var connectionGrid = (Grid)connectionPanel.Child;
                var stepHeader = (Grid)connectionGrid.Children[0];
                var stepBorder = (Border)stepHeader.Children[0];
                
                stepBorder.Style = (Style)FindResource("CompletedStepIndicatorStyle");
                var stepText = (TextBlock)stepBorder.Child;
                stepText.Text = "✓";
                stepText.Foreground = Brushes.White;
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "Could not update step 1 indicator");
            }
        }

        private void UpdateConnectionFromUI(DatabaseConnection connection, bool isSource)
        {
            if (isSource)
            {
                connection.Server = SourceServerTextBox.Text;
                connection.Port = SourcePortTextBox.Text;
                connection.ServiceName = SourceServiceTextBox.Text;
                connection.Username = SourceUsernameTextBox.Text;
                connection.Password = SourcePasswordBox.Password;
            }
            else
            {
                connection.Server = TargetServerTextBox.Text;
                connection.Port = TargetPortTextBox.Text;
                connection.ServiceName = TargetServiceTextBox.Text;
                connection.Username = TargetUsernameTextBox.Text;
                connection.Password = TargetPasswordBox.Password;
            }
        }

        private async void CompareButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate connections first
                if (!_sourceConnection.IsConnected || !_targetConnection.IsConnected)
                {
                    MessageBox.Show("Please test both database connections successfully before comparing schemas.\n\n" +
                                  "Steps:\n1. Enter connection details for both databases\n2. Click 'Test Connection' for each database\n3. Ensure both show '✓ Connected' status", 
                                  "Connection Required", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Start comparison process with enhanced UI feedback
                await StartSchemaComparison();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error during schema comparison");
                MessageBox.Show($"An error occurred during schema comparison:\n\n{ex.Message}\n\nPlease check the logs for more details.", 
                              "Comparison Error", MessageBoxButton.OK, MessageBoxImage.Error);
                ResetComparisonUI();
            }
        }

        private async Task StartSchemaComparison()
        {
            // Update UI to show comparison in progress
            CompareButton.IsEnabled = false;
            CompareButton.Content = "🔄 Comparing...";
            
            // Show detailed progress
            ShowProgress(true, "Initializing schema comparison...");
            ProgressBar.IsIndeterminate = false;
            ProgressBar.Value = 0;
            
            // Update step 2 to active
            UpdateStep2ToActive();

            try
            {
                // Step 1: Get schema filter
                var schemaFilter = string.IsNullOrWhiteSpace(SchemaFilterTextBox.Text) ? null : SchemaFilterTextBox.Text.Trim();
                
                // Step 2: Update progress
                UpdateProgress(10, "Connecting to source database...");
                await Task.Delay(500); // Brief pause for user feedback
                
                // Step 3: Update progress
                UpdateProgress(20, "Connecting to target database...");
                await Task.Delay(500);
                
                // Step 4: Start comparison
                UpdateProgress(30, "Retrieving schema objects...");
                var comparisonResults = await _comparisonService.CompareSchemaObjectsAsync(_sourceConnection, _targetConnection, schemaFilter);
                
                // Step 5: Process results
                UpdateProgress(80, "Processing comparison results...");
                await Task.Delay(300);
                
                // Step 6: Update UI
                UpdateProgress(90, "Updating interface...");
                _schemaObjects.Clear();
                foreach (var obj in comparisonResults)
                {
                    _schemaObjects.Add(obj);
                }
                
                // Step 7: Complete
                UpdateProgress(100, "Comparison completed successfully!");
                await Task.Delay(500);
                
                // Update UI elements
                UpdateStatusBar();
                UpdateResultsSummary();
                
                // Show completion message
                var totalObjects = comparisonResults.Count;
                var identicalCount = comparisonResults.Count(o => o.Status == ComparisonStatus.Identical);
                var differentCount = comparisonResults.Count(o => o.Status == ComparisonStatus.Different);
                var sourceOnlyCount = comparisonResults.Count(o => o.Status == ComparisonStatus.SourceOnly);
                var targetOnlyCount = comparisonResults.Count(o => o.Status == ComparisonStatus.TargetOnly);
                
                StatusBarTextBlock.Text = $"Comparison completed - {totalObjects} objects analyzed";
                StatusIcon.Text = "✅";
                
                // Update step 2 to completed
                UpdateStep2ToCompleted();
                
                // Show summary dialog
                var summaryMessage = $"Schema Comparison Completed Successfully!\n\n" +
                                   $"📊 Total Objects: {totalObjects}\n" +
                                   $"✅ Identical: {identicalCount}\n" +
                                   $"⚠️ Different: {differentCount}\n" +
                                   $"📤 Source Only: {sourceOnlyCount}\n" +
                                   $"📥 Target Only: {targetOnlyCount}\n\n" +
                                   $"Click on any object in the left panel to view detailed differences.";
                
                MessageBox.Show(summaryMessage, "Comparison Results", MessageBoxButton.OK, MessageBoxImage.Information);
                
                // Navigate to results tab
                UpdateTabStates(); // Update tab states first
                NavigateToTab(2); // Navigate to Results tab (index 2)
                
                Log.Information("Schema comparison completed successfully. Found {Count} objects", comparisonResults.Count);
            }
            finally
            {
                ResetComparisonUI();
            }
        }

        private void UpdateProgress(int percentage, string message)
        {
            ProgressBar.Value = percentage;
            ProgressPercentage.Text = $"{percentage}%";
            StatusBarTextBlock.Text = message;
        }

        private void UpdateStep2ToActive()
        {
            // Update step 2 indicator to active state
            var comparePanel = (StackPanel)CompareButton.Parent;
            var step2Border = (Border)comparePanel.Children[0];
            step2Border.Style = (Style)FindResource("ActiveStepIndicatorStyle");
            var step2Text = (TextBlock)step2Border.Child;
            step2Text.Foreground = Brushes.White;
        }

        private void UpdateStep2ToCompleted()
        {
            // Update step 2 indicator to completed state
            var comparePanel = (StackPanel)CompareButton.Parent;
            var step2Border = (Border)comparePanel.Children[0];
            step2Border.Style = (Style)FindResource("CompletedStepIndicatorStyle");
            var step2Text = (TextBlock)step2Border.Child;
            step2Text.Text = "✓";
            step2Text.Foreground = Brushes.White;
        }

        private void ResetComparisonUI()
        {
            CompareButton.IsEnabled = _sourceConnection.IsConnected && _targetConnection.IsConnected;
            CompareButton.Content = "Compare Schemas";
            ShowProgress(false);
        }

        private void ApplyFilterButton_Click(object sender, RoutedEventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            var view = CollectionViewSource.GetDefaultView(_schemaObjects);
            view.Filter = obj =>
            {
                if (obj is not SchemaObject schemaObj) return false;

                // Schema filter
                if (!string.IsNullOrWhiteSpace(SchemaFilterTextBox.Text))
                {
                    if (!schemaObj.Owner.Contains(SchemaFilterTextBox.Text, StringComparison.OrdinalIgnoreCase))
                        return false;
                }

                // Object type filter
                if (ObjectTypeFilterComboBox.SelectedItem != null)
                {
                    if (!schemaObj.ObjectType.Equals(ObjectTypeFilterComboBox.SelectedItem))
                        return false;
                }

                return true;
            };
        }

        private async void SchemaObjectsGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (SchemaObjectsGrid.SelectedItem is SchemaObject selectedObject)
            {
                // Hide empty state and show loading
                EmptyStatePanel.Visibility = Visibility.Collapsed;
                ShowProgress(true, $"Loading details for {selectedObject.FullName}...");
                
                // Update step 4 to active
                UpdateStep4ToActive();
                
                await LoadObjectDetails(selectedObject);
                
                // Update step 4 to completed after loading
                UpdateStep4ToCompleted();
                ShowProgress(false);
            }
            else
            {
                // Show empty state when no selection
                EmptyStatePanel.Visibility = Visibility.Visible;
            }
        }

        private void UpdateStep4ToActive()
        {
            // Find and update step 4 indicator to active state
            try
            {
                var mainGrid = (Grid)this.Content;
                var contentGrid = (Grid)mainGrid.Children[2];
                var detailsPanel = (Border)contentGrid.Children[2];
                var detailsGrid = (Grid)detailsPanel.Child;
                var stepHeader = (Grid)detailsGrid.Children[0];
                var stepBorder = (Border)stepHeader.Children[0];
                
                stepBorder.Style = (Style)FindResource("ActiveStepIndicatorStyle");
                var stepText = (TextBlock)stepBorder.Child;
                stepText.Foreground = Brushes.White;
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "Could not update step 4 indicator to active");
            }
        }

        private void UpdateStep4ToCompleted()
        {
            // Find and update step 4 indicator to completed state
            try
            {
                var mainGrid = (Grid)this.Content;
                var contentGrid = (Grid)mainGrid.Children[2];
                var detailsPanel = (Border)contentGrid.Children[2];
                var detailsGrid = (Grid)detailsPanel.Child;
                var stepHeader = (Grid)detailsGrid.Children[0];
                var stepBorder = (Border)stepHeader.Children[0];
                
                stepBorder.Style = (Style)FindResource("CompletedStepIndicatorStyle");
                var stepText = (TextBlock)stepBorder.Child;
                stepText.Text = "✓";
                stepText.Foreground = Brushes.White;
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "Could not update step 4 indicator to completed");
            }
        }

        private async Task LoadObjectDetails(SchemaObject schemaObject)
        {
            try
            {
                StatusBarTextBlock.Text = $"Loading details for {schemaObject.FullName}...";
                StatusIcon.Text = "⏳";

                if (schemaObject.ObjectType == SchemaObjectType.Table)
                {
                    // Load table columns with progress feedback
                    ColumnsTab.Visibility = Visibility.Visible;
                    DetailsTabControl.SelectedItem = ColumnsTab;

                    StatusBarTextBlock.Text = $"Loading column information for {schemaObject.FullName}...";
                    
                    var sourceColumns = new List<Models.TableColumn>();
                    var targetColumns = new List<Models.TableColumn>();

                    if (schemaObject.Status != ComparisonStatus.TargetOnly)
                    {
                        StatusBarTextBlock.Text = $"Retrieving source columns for {schemaObject.FullName}...";
                        sourceColumns = await _oracleService.GetTableColumnsAsync(_sourceConnection, schemaObject.Owner, schemaObject.Name);
                    }

                    if (schemaObject.Status != ComparisonStatus.SourceOnly)
                    {
                        StatusBarTextBlock.Text = $"Retrieving target columns for {schemaObject.FullName}...";
                        targetColumns = await _oracleService.GetTableColumnsAsync(_targetConnection, schemaObject.Owner, schemaObject.Name);
                    }

                    // Compare columns if both exist
                    if (sourceColumns.Any() && targetColumns.Any())
                    {
                        StatusBarTextBlock.Text = $"Comparing columns for {schemaObject.FullName}...";
                        var comparedColumns = await _comparisonService.CompareTableColumnsAsync(_sourceConnection, _targetConnection, schemaObject.Owner, schemaObject.Name);
                        _tableColumns.Clear();
                        foreach (var col in comparedColumns)
                        {
                            _tableColumns.Add(col);
                        }
                    }
                    else
                    {
                        _tableColumns.Clear();
                        foreach (var col in sourceColumns.Concat(targetColumns))
                        {
                            col.Status = sourceColumns.Any() ? ComparisonStatus.SourceOnly : ComparisonStatus.TargetOnly;
                            _tableColumns.Add(col);
                        }
                    }
                    
                    // Show column summary
                    var columnSummary = GetColumnSummary();
                    StatusBarTextBlock.Text = $"Table {schemaObject.FullName}: {columnSummary}";
                }
                else
                {
                    ColumnsTab.Visibility = Visibility.Collapsed;
                    DetailsTabControl.SelectedItem = DefinitionTab;
                    _tableColumns.Clear();
                }

                // Load object definitions with progress feedback
                StatusBarTextBlock.Text = $"Loading definitions for {schemaObject.FullName}...";
                await LoadObjectDefinitions(schemaObject);

                // Final status update
                StatusBarTextBlock.Text = $"✅ Loaded details for {schemaObject.FullName} - Click tabs to explore";
                StatusIcon.Text = "📋";
            }
            catch (Exception ex)
            {
                StatusBarTextBlock.Text = $"❌ Error loading details for {schemaObject.FullName}";
                StatusIcon.Text = "⚠️";
                Log.Error(ex, "Error loading object details for {ObjectName}", schemaObject.FullName);
                MessageBox.Show($"Error loading object details for {schemaObject.FullName}:\n\n{ex.Message}\n\nThis might be due to insufficient privileges or network connectivity issues.", 
                              "Load Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GetColumnSummary()
        {
            if (!_tableColumns.Any()) return "No columns found";
            
            var identicalCols = _tableColumns.Count(c => c.Status == ComparisonStatus.Identical);
            var differentCols = _tableColumns.Count(c => c.Status == ComparisonStatus.Different);
            var sourceOnlyCols = _tableColumns.Count(c => c.Status == ComparisonStatus.SourceOnly);
            var targetOnlyCols = _tableColumns.Count(c => c.Status == ComparisonStatus.TargetOnly);
            
            var parts = new List<string>();
            if (identicalCols > 0) parts.Add($"{identicalCols} identical");
            if (differentCols > 0) parts.Add($"{differentCols} different");
            if (sourceOnlyCols > 0) parts.Add($"{sourceOnlyCols} source only");
            if (targetOnlyCols > 0) parts.Add($"{targetOnlyCols} target only");
            
            return $"{_tableColumns.Count} columns ({string.Join(", ", parts)})";
        }

        private async Task LoadObjectDefinitions(SchemaObject schemaObject)
        {
            try
            {
                string sourceDefinition = "";
                string targetDefinition = "";

                if (schemaObject.Status != ComparisonStatus.TargetOnly)
                {
                    sourceDefinition = await _oracleService.GetObjectDefinitionAsync(_sourceConnection, schemaObject.Owner, schemaObject.Name, schemaObject.ObjectType);
                }

                if (schemaObject.Status != ComparisonStatus.SourceOnly)
                {
                    targetDefinition = await _oracleService.GetObjectDefinitionAsync(_targetConnection, schemaObject.Owner, schemaObject.Name, schemaObject.ObjectType);
                }

                SourceDefinitionEditor.Text = sourceDefinition;
                TargetDefinitionEditor.Text = targetDefinition;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error loading object definitions for {ObjectName}", schemaObject.FullName);
                SourceDefinitionEditor.Text = $"Error loading definition: {ex.Message}";
                TargetDefinitionEditor.Text = $"Error loading definition: {ex.Message}";
            }
        }

        private void UpdateStatusBar()
        {
            var totalCount = _schemaObjects.Count;
            var identicalCount = _schemaObjects.Count(o => o.Status == ComparisonStatus.Identical);
            var differentCount = _schemaObjects.Count(o => o.Status == ComparisonStatus.Different);
            var sourceOnlyCount = _schemaObjects.Count(o => o.Status == ComparisonStatus.SourceOnly);
            var targetOnlyCount = _schemaObjects.Count(o => o.Status == ComparisonStatus.TargetOnly);

            ObjectCountTextBlock.Text = $"Objects: {totalCount}";
            IdenticalCountTextBlock.Text = $"Identical: {identicalCount}";
            DifferentCountTextBlock.Text = $"Different: {differentCount}";
            SourceOnlyCountTextBlock.Text = $"Source Only: {sourceOnlyCount}";
            TargetOnlyCountTextBlock.Text = $"Target Only: {targetOnlyCount}";
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Settings functionality will be implemented in a future version.", "Settings", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AboutButton_Click(object sender, RoutedEventArgs e)
        {
            var aboutMessage = "Oracle Database Schema Structure Compare\n\n" +
                              "Version: 1.0.0\n" +
                              "Built with WPF and Oracle.ManagedDataAccess\n" +
                              "Material Design Theme with Navy Blue Palette\n\n" +
                              "This application helps you compare Oracle database schemas\n" +
                              "and identify differences between source and target databases.";
            
            MessageBox.Show(aboutMessage, "About", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        #region Enhanced UI/UX Event Handlers

        // Source Database Connection Events
        private void SourceServerTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ValidateConnectionInputs();
            UpdateConnectionStatus();
        }

        private void SourcePortTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ValidateConnectionInputs();
            UpdateConnectionStatus();
        }

        private void SourceServiceTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ValidateConnectionInputs();
            UpdateConnectionStatus();
        }

        private void SourceUsernameTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ValidateConnectionInputs();
            UpdateConnectionStatus();
        }

        private void SourcePasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            ValidateConnectionInputs();
            UpdateConnectionStatus();
        }

        // Target Database Connection Events
        private void TargetServerTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ValidateConnectionInputs();
            UpdateConnectionStatus();
        }

        private void TargetPortTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ValidateConnectionInputs();
            UpdateConnectionStatus();
        }

        private void TargetServiceTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ValidateConnectionInputs();
            UpdateConnectionStatus();
        }

        private void TargetUsernameTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ValidateConnectionInputs();
            UpdateConnectionStatus();
        }

        private void TargetPasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            ValidateConnectionInputs();
            UpdateConnectionStatus();
        }

        private void ValidateConnectionInputs()
        {
            // Check if all controls are initialized
            if (SourceServerTextBox == null || SourcePortTextBox == null || SourceServiceTextBox == null ||
                SourceUsernameTextBox == null || SourcePasswordBox == null ||
                TargetServerTextBox == null || TargetPortTextBox == null || TargetServiceTextBox == null ||
                TargetUsernameTextBox == null || TargetPasswordBox == null ||
                TestSourceButton == null || TestTargetButton == null)
            {
                return; // Controls not yet initialized
            }

            // Validate source connection inputs
            bool sourceValid = !string.IsNullOrWhiteSpace(SourceServerTextBox.Text) &&
                              !string.IsNullOrWhiteSpace(SourcePortTextBox.Text) &&
                              !string.IsNullOrWhiteSpace(SourceServiceTextBox.Text) &&
                              !string.IsNullOrWhiteSpace(SourceUsernameTextBox.Text) &&
                              !string.IsNullOrWhiteSpace(SourcePasswordBox.Password);

            // Validate target connection inputs
            bool targetValid = !string.IsNullOrWhiteSpace(TargetServerTextBox.Text) &&
                              !string.IsNullOrWhiteSpace(TargetPortTextBox.Text) &&
                              !string.IsNullOrWhiteSpace(TargetServiceTextBox.Text) &&
                              !string.IsNullOrWhiteSpace(TargetUsernameTextBox.Text) &&
                              !string.IsNullOrWhiteSpace(TargetPasswordBox.Password);

            // Enable test buttons based on validation
            TestSourceButton.IsEnabled = sourceValid;
            TestTargetButton.IsEnabled = targetValid;
        }

        private void UpdateConnectionStatus()
        {
            // Check if controls are initialized
            if (SourceStatusIndicator == null || TargetStatusIndicator == null ||
                SourceConnectionStatus == null || TargetConnectionStatus == null)
            {
                return; // Controls not yet initialized
            }

            // Reset connection status indicators
            SourceStatusIndicator.Background = (SolidColorBrush)FindResource("ProgressBackgroundBrush");
            TargetStatusIndicator.Background = (SolidColorBrush)FindResource("ProgressBackgroundBrush");
            
            // Hide previous status messages
            SourceConnectionStatus.Visibility = Visibility.Collapsed;
            TargetConnectionStatus.Visibility = Visibility.Collapsed;
            
            // Update compare button availability
            UpdateCompareButtonState();
            
            // Update status bar
            UpdateStatusBarMessage();
        }

        private void UpdateCompareButtonState()
        {
            // Check if CompareButton is initialized
            if (CompareButton == null)
            {
                return; // Control not yet initialized
            }

            bool canCompare = _sourceConnection.IsConnected && _targetConnection.IsConnected;
            CompareButton.IsEnabled = canCompare;
            
            if (canCompare)
            {
                try
                {
                    // Update step 2 indicator to active
                    var step2Border = (Border)((StackPanel)CompareButton.Parent).Children[0];
                    step2Border.Style = (Style)FindResource("ActiveStepIndicatorStyle");
                    var step2Text = (TextBlock)step2Border.Child;
                    step2Text.Foreground = Brushes.White;
                }
                catch (Exception ex)
                {
                    Log.Warning(ex, "Could not update step 2 indicator");
                }
            }
        }

        private void UpdateStatusBarMessage()
        {
            // Check if status bar controls are initialized
            if (StatusBarTextBlock == null || StatusIcon == null)
            {
                return; // Controls not yet initialized
            }

            if (!_sourceConnection.IsConnected && !_targetConnection.IsConnected)
            {
                StatusBarTextBlock.Text = "Ready to start - Configure database connections";
                StatusIcon.Text = "🔄";
            }
            else if (_sourceConnection.IsConnected && !_targetConnection.IsConnected)
            {
                StatusBarTextBlock.Text = "Source connected - Configure target database";
                StatusIcon.Text = "⚡";
            }
            else if (!_sourceConnection.IsConnected && _targetConnection.IsConnected)
            {
                StatusBarTextBlock.Text = "Target connected - Configure source database";
                StatusIcon.Text = "⚡";
            }
            else if (_sourceConnection.IsConnected && _targetConnection.IsConnected)
            {
                StatusBarTextBlock.Text = "Both databases connected - Ready to compare";
                StatusIcon.Text = "✅";
            }
        }

        private void UpdateStatusBarMessage(string message, string icon)
        {
            // Check if status bar controls are initialized
            if (StatusBarTextBlock == null || StatusIcon == null)
            {
                return; // Controls not yet initialized
            }

            StatusBarTextBlock.Text = message;
            StatusIcon.Text = icon;
        }

        private void ShowConnectionStatus(string elementName, bool success, string message)
        {
            TextBlock statusElement = null;
            Border indicatorElement = null;
            
            if (elementName == "Source")
            {
                statusElement = SourceConnectionStatus;
                indicatorElement = SourceStatusIndicator;
            }
            else if (elementName == "Target")
            {
                statusElement = TargetConnectionStatus;
                indicatorElement = TargetStatusIndicator;
            }

            if (statusElement != null && indicatorElement != null)
            {
                statusElement.Text = message;
                statusElement.Visibility = Visibility.Visible;
                
                if (success)
                {
                    statusElement.Foreground = (SolidColorBrush)FindResource("SuccessBrush");
                    indicatorElement.Background = (SolidColorBrush)FindResource("SuccessBrush");
                }
                else
                {
                    statusElement.Foreground = (SolidColorBrush)FindResource("ErrorBrush");
                    indicatorElement.Background = (SolidColorBrush)FindResource("ErrorBrush");
                }
            }
        }

        private void ShowProgress(bool show, string message = "")
        {
            if (show)
            {
                ConnectionProgressBar.Visibility = Visibility.Visible;
                ProgressContainer.Visibility = Visibility.Visible;
                StatusSpinner.Visibility = Visibility.Visible;
                LoadingSpinner.Visibility = Visibility.Visible;
                StatusBarTextBlock.Text = message;
                StatusIcon.Text = "⏳";
            }
            else
            {
                ConnectionProgressBar.Visibility = Visibility.Collapsed;
                ProgressContainer.Visibility = Visibility.Collapsed;
                StatusSpinner.Visibility = Visibility.Collapsed;
                LoadingSpinner.Visibility = Visibility.Collapsed;
            }
        }

        private void UpdateResultsSummary()
        {
            if (_schemaObjects.Any())
            {
                ResultsSummaryPanel.Visibility = Visibility.Visible;
                
                var identicalCount = _schemaObjects.Count(o => o.Status == ComparisonStatus.Identical);
                var differentCount = _schemaObjects.Count(o => o.Status == ComparisonStatus.Different);
                var sourceOnlyCount = _schemaObjects.Count(o => o.Status == ComparisonStatus.SourceOnly);
                var targetOnlyCount = _schemaObjects.Count(o => o.Status == ComparisonStatus.TargetOnly);

                IdenticalCountText.Text = identicalCount.ToString();
                DifferentCountText.Text = differentCount.ToString();
                SourceOnlyCountText.Text = sourceOnlyCount.ToString();
                TargetOnlyCountText.Text = targetOnlyCount.ToString();
                
                // Update step 3 indicator to completed
                var step3Panel = (Grid)ResultsSummaryPanel.Parent;
                var step3Header = (Grid)step3Panel.Children[0];
                var step3Border = (Border)step3Header.Children[0];
                step3Border.Style = (Style)FindResource("CompletedStepIndicatorStyle");
                var step3Text = (TextBlock)step3Border.Child;
                step3Text.Text = "✓";
                step3Text.Foreground = Brushes.White;
            }
            else
            {
                ResultsSummaryPanel.Visibility = Visibility.Collapsed;
            }
        }

        #endregion
    }
}