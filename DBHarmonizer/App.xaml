<Application x:Class="DBHarmonizer.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DBHarmonizer"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Telerik Material Theme -->
                <ResourceDictionary Source="/Telerik.Windows.Themes.Material;component/Themes/System.Windows.xaml"/>
                <ResourceDictionary Source="/Telerik.Windows.Themes.Material;component/Themes/Telerik.Windows.Controls.xaml"/>
                <ResourceDictionary Source="/Telerik.Windows.Themes.Material;component/Themes/Telerik.Windows.Controls.Input.xaml"/>
                <ResourceDictionary Source="/Telerik.Windows.Themes.Material;component/Themes/Telerik.Windows.Controls.Navigation.xaml"/>
                <ResourceDictionary Source="/Telerik.Windows.Themes.Material;component/Themes/Telerik.Windows.Controls.Data.xaml"/>
                <ResourceDictionary Source="/Telerik.Windows.Themes.Material;component/Themes/Telerik.Windows.Controls.GridView.xaml"/>
                <ResourceDictionary Source="/Telerik.Windows.Themes.Material;component/Themes/Telerik.Windows.Controls.Docking.xaml"/>
                
                <!-- Custom Navy Blue Color Palette -->
                <ResourceDictionary>
                    <!-- Primary Navy Blue Colors -->
                    <SolidColorBrush x:Key="PrimaryNavyBrush" Color="#1E3A8A"/>
                    <SolidColorBrush x:Key="SecondaryNavyBrush" Color="#3B82F6"/>
                    <SolidColorBrush x:Key="LightNavyBrush" Color="#60A5FA"/>
                    <SolidColorBrush x:Key="DarkNavyBrush" Color="#1E40AF"/>
                    <SolidColorBrush x:Key="AccentNavyBrush" Color="#2563EB"/>
                    
                    <!-- Background Colors -->
                    <SolidColorBrush x:Key="MainBackgroundBrush" Color="#F8FAFC"/>
                    <SolidColorBrush x:Key="SecondaryBackgroundBrush" Color="#F1F5F9"/>
                    <SolidColorBrush x:Key="CardBackgroundBrush" Color="#FFFFFF"/>
                    
                    <!-- Text Colors -->
                    <SolidColorBrush x:Key="PrimaryTextBrush" Color="#0F172A"/>
                    <SolidColorBrush x:Key="SecondaryTextBrush" Color="#475569"/>
                    <SolidColorBrush x:Key="AccentTextBrush" Color="#1E3A8A"/>
                    
                    <!-- Border Colors -->
                    <SolidColorBrush x:Key="BorderBrush" Color="#E2E8F0"/>
                    <SolidColorBrush x:Key="FocusBorderBrush" Color="#3B82F6"/>
                    
                    <!-- Status Colors -->
                    <SolidColorBrush x:Key="SuccessBrush" Color="#10B981"/>
                    <SolidColorBrush x:Key="WarningBrush" Color="#F59E0B"/>
                    <SolidColorBrush x:Key="ErrorBrush" Color="#EF4444"/>
                    <SolidColorBrush x:Key="InfoBrush" Color="#3B82F6"/>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
