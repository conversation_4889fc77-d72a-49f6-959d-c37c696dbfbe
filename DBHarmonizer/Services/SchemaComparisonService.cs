using DBHarmonizer.Models;
using Serilog;

namespace DBHarmonizer.Services
{
    public class SchemaComparisonService
    {
        private readonly OracleSchemaService _oracleService;

        public SchemaComparisonService()
        {
            _oracleService = new OracleSchemaService();
        }

        public async Task<List<SchemaObject>> CompareSchemaObjectsAsync(
            DatabaseConnection sourceConnection, 
            DatabaseConnection targetConnection,
            string? schemaFilter = null)
        {
            Log.Information("Starting schema comparison between {Source} and {Target}", 
                sourceConnection.Server, targetConnection.Server);

            var sourceObjects = await _oracleService.GetSchemaObjectsAsync(sourceConnection, schemaFilter);
            var targetObjects = await _oracleService.GetSchemaObjectsAsync(targetConnection, schemaFilter);

            var comparisonResults = new List<SchemaObject>();

            // Create lookup dictionaries for faster comparison
            var sourceDict = sourceObjects.ToDictionary(obj => $"{obj.Owner}.{obj.Name}.{obj.ObjectType}", obj => obj);
            var targetDict = targetObjects.ToDictionary(obj => $"{obj.Owner}.{obj.Name}.{obj.ObjectType}", obj => obj);

            // Find objects that exist in both databases
            foreach (var sourceObj in sourceObjects)
            {
                var key = $"{sourceObj.Owner}.{sourceObj.Name}.{sourceObj.ObjectType}";
                if (targetDict.ContainsKey(key))
                {
                    var targetObj = targetDict[key];
                    
                    // For now, mark as identical - detailed comparison would require DDL comparison
                    sourceObj.Status = ComparisonStatus.Identical;
                    comparisonResults.Add(sourceObj);
                    
                    // Remove from target dict to track processed items
                    targetDict.Remove(key);
                }
                else
                {
                    // Object exists only in source
                    sourceObj.Status = ComparisonStatus.SourceOnly;
                    comparisonResults.Add(sourceObj);
                }
            }

            // Remaining objects in target dict exist only in target
            foreach (var targetObj in targetDict.Values)
            {
                targetObj.Status = ComparisonStatus.TargetOnly;
                comparisonResults.Add(targetObj);
            }

            Log.Information("Schema comparison completed. Found {Total} objects: {Identical} identical, {SourceOnly} source-only, {TargetOnly} target-only",
                comparisonResults.Count,
                comparisonResults.Count(o => o.Status == ComparisonStatus.Identical),
                comparisonResults.Count(o => o.Status == ComparisonStatus.SourceOnly),
                comparisonResults.Count(o => o.Status == ComparisonStatus.TargetOnly));

            return comparisonResults.OrderBy(o => o.Owner).ThenBy(o => o.ObjectType).ThenBy(o => o.Name).ToList();
        }

        public async Task<List<TableColumn>> CompareTableColumnsAsync(
            DatabaseConnection sourceConnection,
            DatabaseConnection targetConnection,
            string owner,
            string tableName)
        {
            Log.Information("Comparing columns for table {Owner}.{TableName}", owner, tableName);

            var sourceColumns = await _oracleService.GetTableColumnsAsync(sourceConnection, owner, tableName);
            var targetColumns = await _oracleService.GetTableColumnsAsync(targetConnection, owner, tableName);

            var comparisonResults = new List<TableColumn>();

            // Create lookup dictionary for target columns
            var targetDict = targetColumns.ToDictionary(col => col.ColumnName, col => col);

            // Compare source columns
            foreach (var sourceCol in sourceColumns)
            {
                if (targetDict.ContainsKey(sourceCol.ColumnName))
                {
                    var targetCol = targetDict[sourceCol.ColumnName];
                    
                    // Compare column properties
                    if (AreColumnsIdentical(sourceCol, targetCol))
                    {
                        sourceCol.Status = ComparisonStatus.Identical;
                    }
                    else
                    {
                        sourceCol.Status = ComparisonStatus.Different;
                    }
                    
                    comparisonResults.Add(sourceCol);
                    targetDict.Remove(sourceCol.ColumnName);
                }
                else
                {
                    sourceCol.Status = ComparisonStatus.SourceOnly;
                    comparisonResults.Add(sourceCol);
                }
            }

            // Add remaining target columns (exist only in target)
            foreach (var targetCol in targetDict.Values)
            {
                targetCol.Status = ComparisonStatus.TargetOnly;
                comparisonResults.Add(targetCol);
            }

            return comparisonResults.OrderBy(c => c.ColumnName).ToList();
        }

        private static bool AreColumnsIdentical(TableColumn source, TableColumn target)
        {
            return source.DataType == target.DataType &&
                   source.DataLength == target.DataLength &&
                   source.DataPrecision == target.DataPrecision &&
                   source.DataScale == target.DataScale &&
                   source.Nullable == target.Nullable &&
                   source.DefaultValue == target.DefaultValue &&
                   source.IsPrimaryKey == target.IsPrimaryKey &&
                   source.IsForeignKey == target.IsForeignKey;
        }

        public async Task<(string sourceDefinition, string targetDefinition)> GetObjectDefinitionsAsync(
            DatabaseConnection sourceConnection,
            DatabaseConnection targetConnection,
            string owner,
            string objectName,
            SchemaObjectType objectType)
        {
            var sourceTask = _oracleService.GetObjectDefinitionAsync(sourceConnection, owner, objectName, objectType);
            var targetTask = _oracleService.GetObjectDefinitionAsync(targetConnection, owner, objectName, objectType);

            await Task.WhenAll(sourceTask, targetTask);

            return (await sourceTask, await targetTask);
        }
    }
}