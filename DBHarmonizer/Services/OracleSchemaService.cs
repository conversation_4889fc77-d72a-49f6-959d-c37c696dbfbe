using Oracle.ManagedDataAccess.Client;
using System.Data;
using DBHarmonizer.Models;
using Serilog;

namespace DBHarmonizer.Services
{
    public class OracleSchemaService
    {
        public async Task<bool> TestConnectionAsync(DatabaseConnection connection)
        {
            try
            {
                using var oracleConnection = new OracleConnection(connection.ConnectionString);
                await oracleConnection.OpenAsync();
                Log.Information("Successfully connected to Oracle database: {Server}", connection.Server);
                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to connect to Oracle database: {Server}", connection.Server);
                return false;
            }
        }

        public async Task<List<SchemaObject>> GetSchemaObjectsAsync(DatabaseConnection connection, string? schemaFilter = null)
        {
            var schemaObjects = new List<SchemaObject>();

            try
            {
                using var oracleConnection = new OracleConnection(connection.ConnectionString);
                await oracleConnection.OpenAsync();

                var sql = @"
                    SELECT 
                        OWNER,
                        OBJECT_NAME,
                        OBJECT_TYPE,
                        STATUS,
                        CREATED,
                        LAST_DDL_TIME
                    FROM ALL_OBJECTS 
                    WHERE OBJECT_TYPE IN ('TABLE', 'VIEW', 'INDEX', 'SEQUENCE', 'TRIGGER', 'PROCEDURE', 'FUNCTION', 'PACKAGE', 'SYNONYM')
                    AND OWNER NOT IN ('SYS', 'SYSTEM', 'DBSNMP', 'SYSMAN', 'OUTLN', 'MDSYS', 'ORDSYS', 'EXFSYS', 'DMSYS', 'WMSYS', 'CTXSYS', 'ANONYMOUS', 'XDB', 'XS$NULL', 'ORACLE_OCM', 'APPQOSSYS')";

                if (!string.IsNullOrEmpty(schemaFilter))
                {
                    sql += " AND UPPER(OWNER) = UPPER(:schemaFilter)";
                }

                sql += " ORDER BY OWNER, OBJECT_TYPE, OBJECT_NAME";

                using var command = new OracleCommand(sql, oracleConnection);
                if (!string.IsNullOrEmpty(schemaFilter))
                {
                    command.Parameters.Add(":schemaFilter", OracleDbType.Varchar2).Value = schemaFilter;
                }

                using var reader = await command.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    var schemaObject = new SchemaObject
                    {
                        Owner = reader.GetString("OWNER"),
                        Name = reader.GetString("OBJECT_NAME"),
                        ObjectType = ParseObjectType(reader.GetString("OBJECT_TYPE"))
                    };

                    schemaObjects.Add(schemaObject);
                }

                Log.Information("Retrieved {Count} schema objects from {Server}", schemaObjects.Count, connection.Server);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to retrieve schema objects from {Server}", connection.Server);
                throw;
            }

            return schemaObjects;
        }

        public async Task<List<TableColumn>> GetTableColumnsAsync(DatabaseConnection connection, string owner, string tableName)
        {
            var columns = new List<TableColumn>();

            try
            {
                using var oracleConnection = new OracleConnection(connection.ConnectionString);
                await oracleConnection.OpenAsync();

                var sql = @"
                    SELECT 
                        c.COLUMN_NAME,
                        c.DATA_TYPE,
                        c.DATA_LENGTH,
                        c.DATA_PRECISION,
                        c.DATA_SCALE,
                        c.NULLABLE,
                        c.DATA_DEFAULT,
                        cc.COMMENTS,
                        CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 'Y' ELSE 'N' END AS IS_PRIMARY_KEY,
                        CASE WHEN fk.COLUMN_NAME IS NOT NULL THEN 'Y' ELSE 'N' END AS IS_FOREIGN_KEY
                    FROM ALL_TAB_COLUMNS c
                    LEFT JOIN ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                    LEFT JOIN (
                        SELECT acc.OWNER, acc.TABLE_NAME, acc.COLUMN_NAME
                        FROM ALL_CONS_COLUMNS acc
                        JOIN ALL_CONSTRAINTS ac ON acc.OWNER = ac.OWNER AND acc.CONSTRAINT_NAME = ac.CONSTRAINT_NAME
                        WHERE ac.CONSTRAINT_TYPE = 'P'
                    ) pk ON c.OWNER = pk.OWNER AND c.TABLE_NAME = pk.TABLE_NAME AND c.COLUMN_NAME = pk.COLUMN_NAME
                    LEFT JOIN (
                        SELECT acc.OWNER, acc.TABLE_NAME, acc.COLUMN_NAME
                        FROM ALL_CONS_COLUMNS acc
                        JOIN ALL_CONSTRAINTS ac ON acc.OWNER = ac.OWNER AND acc.CONSTRAINT_NAME = ac.CONSTRAINT_NAME
                        WHERE ac.CONSTRAINT_TYPE = 'R'
                    ) fk ON c.OWNER = fk.OWNER AND c.TABLE_NAME = fk.TABLE_NAME AND c.COLUMN_NAME = fk.COLUMN_NAME
                    WHERE c.OWNER = :owner AND c.TABLE_NAME = :tableName
                    ORDER BY c.COLUMN_ID";

                using var command = new OracleCommand(sql, oracleConnection);
                command.Parameters.Add(":owner", OracleDbType.Varchar2).Value = owner;
                command.Parameters.Add(":tableName", OracleDbType.Varchar2).Value = tableName;

                using var reader = await command.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    var column = new TableColumn
                    {
                        ColumnName = reader.GetString("COLUMN_NAME"),
                        DataType = reader.GetString("DATA_TYPE"),
                        DataLength = reader.IsDBNull("DATA_LENGTH") ? null : reader.GetInt32("DATA_LENGTH"),
                        DataPrecision = reader.IsDBNull("DATA_PRECISION") ? null : reader.GetInt32("DATA_PRECISION"),
                        DataScale = reader.IsDBNull("DATA_SCALE") ? null : reader.GetInt32("DATA_SCALE"),
                        Nullable = reader.GetString("NULLABLE") == "Y",
                        DefaultValue = reader.IsDBNull("DATA_DEFAULT") ? null : reader.GetString("DATA_DEFAULT")?.Trim(),
                        Comments = reader.IsDBNull("COMMENTS") ? string.Empty : reader.GetString("COMMENTS"),
                        IsPrimaryKey = reader.GetString("IS_PRIMARY_KEY") == "Y",
                        IsForeignKey = reader.GetString("IS_FOREIGN_KEY") == "Y"
                    };

                    columns.Add(column);
                }

                Log.Information("Retrieved {Count} columns for table {Owner}.{TableName}", columns.Count, owner, tableName);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to retrieve columns for table {Owner}.{TableName}", owner, tableName);
                throw;
            }

            return columns;
        }

        public async Task<string> GetObjectDefinitionAsync(DatabaseConnection connection, string owner, string objectName, SchemaObjectType objectType)
        {
            try
            {
                using var oracleConnection = new OracleConnection(connection.ConnectionString);
                await oracleConnection.OpenAsync();

                string sql = objectType switch
                {
                    SchemaObjectType.Table => GetTableDefinitionSql(),
                    SchemaObjectType.View => "SELECT TEXT FROM ALL_VIEWS WHERE OWNER = :owner AND VIEW_NAME = :objectName",
                    SchemaObjectType.Procedure or SchemaObjectType.Function or SchemaObjectType.Package => 
                        "SELECT TEXT FROM ALL_SOURCE WHERE OWNER = :owner AND NAME = :objectName ORDER BY LINE",
                    _ => "SELECT 'Definition not available for this object type' FROM DUAL"
                };

                using var command = new OracleCommand(sql, oracleConnection);
                command.Parameters.Add(":owner", OracleDbType.Varchar2).Value = owner;
                command.Parameters.Add(":objectName", OracleDbType.Varchar2).Value = objectName;

                if (objectType == SchemaObjectType.Procedure || objectType == SchemaObjectType.Function || objectType == SchemaObjectType.Package)
                {
                    var definition = new System.Text.StringBuilder();
                    using var reader = await command.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        definition.Append(reader.GetString("TEXT"));
                    }
                    return definition.ToString();
                }
                else
                {
                    var result = await command.ExecuteScalarAsync();
                    return result?.ToString() ?? string.Empty;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to retrieve definition for {Owner}.{ObjectName}", owner, objectName);
                return $"Error retrieving definition: {ex.Message}";
            }
        }

        private static SchemaObjectType ParseObjectType(string objectType)
        {
            return objectType.ToUpper() switch
            {
                "TABLE" => SchemaObjectType.Table,
                "VIEW" => SchemaObjectType.View,
                "INDEX" => SchemaObjectType.Index,
                "SEQUENCE" => SchemaObjectType.Sequence,
                "TRIGGER" => SchemaObjectType.Trigger,
                "PROCEDURE" => SchemaObjectType.Procedure,
                "FUNCTION" => SchemaObjectType.Function,
                "PACKAGE" => SchemaObjectType.Package,
                "SYNONYM" => SchemaObjectType.Synonym,
                _ => SchemaObjectType.Table
            };
        }

        private static string GetTableDefinitionSql()
        {
            return @"
                SELECT 
                    'CREATE TABLE ' || OWNER || '.' || TABLE_NAME || ' (' || CHR(10) ||
                    LISTAGG(
                        '  ' || COLUMN_NAME || ' ' || 
                        CASE 
                            WHEN DATA_TYPE IN ('VARCHAR2', 'CHAR', 'NVARCHAR2', 'NCHAR') THEN DATA_TYPE || '(' || DATA_LENGTH || ')'
                            WHEN DATA_TYPE = 'NUMBER' AND DATA_PRECISION IS NOT NULL AND DATA_SCALE IS NOT NULL THEN DATA_TYPE || '(' || DATA_PRECISION || ',' || DATA_SCALE || ')'
                            WHEN DATA_TYPE = 'NUMBER' AND DATA_PRECISION IS NOT NULL THEN DATA_TYPE || '(' || DATA_PRECISION || ')'
                            ELSE DATA_TYPE
                        END ||
                        CASE WHEN NULLABLE = 'N' THEN ' NOT NULL' ELSE '' END ||
                        CASE WHEN DATA_DEFAULT IS NOT NULL THEN ' DEFAULT ' || DATA_DEFAULT ELSE '' END,
                        ',' || CHR(10)
                    ) WITHIN GROUP (ORDER BY COLUMN_ID) || CHR(10) || ');' AS TABLE_DDL
                FROM ALL_TAB_COLUMNS
                WHERE OWNER = :owner AND TABLE_NAME = :objectName
                GROUP BY OWNER, TABLE_NAME";
        }
    }
}