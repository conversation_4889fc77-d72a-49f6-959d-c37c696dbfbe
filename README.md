# DBHarmonizer - Oracle Database Schema Comparison Tool

A production-ready WPF application for comparing Oracle database schemas with a modern Material Design interface using Telerik UI controls and a Navy Blue color palette.

## Features

### 🔍 **Comprehensive Schema Comparison**
- **Tables**: Compare structure, columns, indexes, constraints, and row counts
- **Views**: Compare definitions and metadata
- **Procedures**: Compare source code and parameters
- **Functions**: Compare source code, parameters, and return types
- **Indexes**: Compare structure and properties
- **Constraints**: Compare definitions and relationships

### 🎨 **Modern UI/UX**
- **Material Design**: Clean, modern interface using Telerik Material theme
- **Navy Blue Color Palette**: Professional and consistent visual design
- **Step-by-Step Workflow**: Guided 4-step process with visual progress indicators
- **Responsive Layout**: Adaptive interface that works on different screen sizes

### 🔧 **Production-Ready Architecture**
- **MVVM Pattern**: Clean separation of concerns with proper data binding
- **Dependency Injection**: Modular architecture using Microsoft.Extensions.DependencyInjection
- **Async/Await**: Non-blocking operations with proper cancellation support
- **Comprehensive Logging**: Structured logging using Serilog
- **Error Handling**: Robust error handling with user-friendly messages

### 📊 **Export Capabilities**
- **Excel Reports**: Multi-worksheet Excel files with detailed comparison results
- **CSV Export**: Simple comma-separated format for data analysis
- **HTML Reports**: Styled HTML reports for sharing and presentation
- **DDL Scripts**: Generate SQL scripts for schema synchronization

### 🔐 **Enterprise Features**
- **Connection Management**: Secure connection string handling
- **Configuration**: JSON-based configuration with environment support
- **Performance Monitoring**: Built-in performance tracking and optimization
- **Cancellation Support**: Cancel long-running operations gracefully

## Technology Stack

- **.NET 6.0**: Modern .NET framework with improved performance
- **WPF**: Rich desktop application framework
- **Telerik UI for WPF**: Premium UI controls with Material Design theme
- **Oracle.ManagedDataAccess.Core**: Official Oracle database connectivity
- **Serilog**: Structured logging framework
- **ClosedXML**: Excel file generation and manipulation
- **Microsoft.Extensions**: Dependency injection and configuration

## Getting Started

### Prerequisites
- .NET 6.0 SDK or later
- Visual Studio 2022 or JetBrains Rider
- Oracle Database 11g or later
- Telerik UI for WPF license (for UI controls)

### Installation
1. Clone the repository
2. Open `DBHarmonizer.sln` in Visual Studio
3. Restore NuGet packages
4. Build the solution
5. Run the application

### Configuration
The application uses `appsettings.json` for configuration:

```json
{
  "DBHarmonizer": {
    "Database": {
      "ConnectionTimeout": 30,
      "CommandTimeout": 300
    },
    "Comparison": {
      "MaxConcurrentOperations": 5,
      "EnableDetailedLogging": true
    },
    "Export": {
      "DefaultExportPath": "exports",
      "MaxFileSizeMB": 100
    }
  }
}
```

## Usage

### 1. **Connect to Databases**
- Enter source and target database connection details
- Test connections to verify connectivity
- Proceed to comparison step

### 2. **Compare Schemas**
- Select schemas to compare
- Monitor progress with real-time updates
- View detailed comparison results

### 3. **Review Results**
- Examine differences in tabbed interface
- Filter and sort results
- View detailed object comparisons

### 4. **Export Reports**
- Choose export format (Excel, CSV, HTML, SQL)
- Generate comprehensive reports
- Save DDL scripts for synchronization

## Architecture

### Service Layer
- **IConnectionService**: Database connection management
- **ISchemaDiscoveryService**: Schema object discovery
- **ISchemaComparisonService**: Schema comparison logic
- **IExportService**: Report generation and export

### Models
- **DatabaseSchema**: Complete schema representation
- **SchemaObject**: Base class for all database objects
- **ComparisonResult**: Detailed comparison results
- **DatabaseConnection**: Connection configuration

### ViewModels
- **MainWindowViewModel**: Primary application logic
- **MVVM Pattern**: Clean separation with proper data binding
- **Command Pattern**: Async commands with cancellation support

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the logs in the `logs/` directory

## Roadmap

- [ ] Support for additional database types (SQL Server, PostgreSQL)
- [ ] Advanced filtering and search capabilities
- [ ] Automated schema synchronization
- [ ] REST API for integration
- [ ] Command-line interface
- [ ] Docker containerization
